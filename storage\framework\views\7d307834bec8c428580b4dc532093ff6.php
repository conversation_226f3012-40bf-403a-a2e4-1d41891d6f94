<div class="tab-pane fade" id="other" role="tabpanel">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">Penanggung Jawab Produk</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class='bx bx-info-circle'></i>
                Penanggung jawab produk akan menangani orderan yang masuk untuk produk ini.
                Hanya CS yang dapat menjadi penanggung jawab produk.
            </div>

            <div class="row">
                <div class="col-md-12">
                    <!-- Distribution Type Selection -->
                    <div class="mb-3">
                        <label class="form-label">Tipe Distribusi <span class="text-danger">*</span></label>
                        <select id="distribution_type" name="distribution_type" class="form-select" required>
                            <option value="equal" <?php echo e(old('distribution_type', isset($product) && $product->responsibleUsers->isNotEmpty() ? $product->responsibleUsers->first()->distribution_type : 'equal') == 'equal' ? 'selected' : ''); ?>>
                                Sama Rata (Equal)
                            </option>
                            <option value="percentage" <?php echo e(old('distribution_type', isset($product) && $product->responsibleUsers->isNotEmpty() ? $product->responsibleUsers->first()->distribution_type : '') == 'percentage' ? 'selected' : ''); ?>>
                                Persentase (Percentage)
                            </option>
                            <option value="fixed" <?php echo e(old('distribution_type', isset($product) && $product->responsibleUsers->isNotEmpty() ? $product->responsibleUsers->first()->distribution_type : '') == 'fixed' ? 'selected' : ''); ?>>
                                Tetap (Fixed)
                            </option>
                        </select>
                        <div class="form-text">
                            Pilih metode distribusi order ke Customer Service
                        </div>
                        <?php $__errorArgs = ['distribution_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Responsible Users Selection -->
                    <div class="mb-3">
                        <label class="form-label">Pilih Penanggung Jawab <span class="text-danger">*</span></label>
                        <select id="responsible_users" name="responsible_users[]" class="form-select" multiple required>
                            <?php $__currentLoopData = $users->where('level_user', 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($user->id); ?>"
                                        <?php if(in_array($user->id, old('responsible_users', isset($product) ? $product->responsibleUsers->pluck('user_id')->toArray() : []))): echo 'selected'; endif; ?>
                                        data-distribution-type="<?php echo e(isset($product) ? $product->responsibleUsers->where('user_id', $user->id)->first()->distribution_type ?? '' : ''); ?>"
                                        data-distribution-value="<?php echo e(isset($product) ? $product->responsibleUsers->where('user_id', $user->id)->first()->distribution_value ?? '' : ''); ?>">
                                    <?php echo e($user->name); ?> - <?php echo e($user->email); ?>

                                    <?php if(isset($product) && $product->responsibleUsers->where('user_id', $user->id)->isNotEmpty()): ?>
                                        <?php $ru = $product->responsibleUsers->where('user_id', $user->id)->first(); ?>
                                        (<?php echo e($ru->distribution_type == 'percentage' ? $ru->distribution_value . '%' : ($ru->distribution_type == 'fixed' ? $ru->distribution_value . ' order' : 'equal')); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div class="form-text">
                            Pilih satu atau lebih penanggung jawab yang akan menangani orderan produk ini
                        </div>
                        <?php $__errorArgs = ['responsible_users'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Distribution Values Container -->
                    <div id="distribution_values_container" class="card mb-3" style="display: none;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <h5 class="card-title mb-0">Atur Nilai Distribusi</h5>
                                <?php if(isset($product) && $product->responsibleUsers->isNotEmpty()): ?>
                                    <span class="badge bg-info ms-2">
                                        <i class="bx bx-info-circle"></i> Mode Edit
                                    </span>
                                <?php endif; ?>
                            </div>

                            <?php if(isset($product) && $product->responsibleUsers->isNotEmpty()): ?>
                                <div class="alert alert-info mb-3">
                                    <i class="bx bx-lightbulb"></i>
                                    <strong>Nilai yang ada:</strong> Sistem akan menampilkan nilai distribusi yang sudah tersimpan untuk setiap penanggung jawab.
                                </div>
                            <?php endif; ?>

                            <div id="distribution_values_inputs">
                                <!-- Dynamic inputs will be added here by jQuery -->
                            </div>
                            <div id="distribution_errors" class="mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\jualinn\resources\views/data-master/product/partials/other.blade.php ENDPATH**/ ?>