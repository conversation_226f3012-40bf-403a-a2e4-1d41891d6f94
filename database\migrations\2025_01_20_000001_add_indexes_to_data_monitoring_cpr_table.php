<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('data_monitoring_cpr', function (Blueprint $table) {
            // Add indexes for better query performance
            $table->index('id_akun', 'idx_data_monitoring_cpr_id_akun');
            $table->index('id_kampanye', 'idx_data_monitoring_cpr_id_kampanye');
            $table->index('status', 'idx_data_monitoring_cpr_status');
            $table->index('tanggal_mulai', 'idx_data_monitoring_cpr_tanggal_mulai');
            $table->index(['id_akun', 'status'], 'idx_data_monitoring_cpr_akun_status');
            $table->index(['tanggal_mulai', 'tanggal_berhenti'], 'idx_data_monitoring_cpr_date_range');
            
            // Composite index for common query patterns
            $table->index(['id_akun', 'tanggal_mulai', 'status'], 'idx_data_monitoring_cpr_common_query');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('data_monitoring_cpr', function (Blueprint $table) {
            $table->dropIndex('idx_data_monitoring_cpr_id_akun');
            $table->dropIndex('idx_data_monitoring_cpr_id_kampanye');
            $table->dropIndex('idx_data_monitoring_cpr_status');
            $table->dropIndex('idx_data_monitoring_cpr_tanggal_mulai');
            $table->dropIndex('idx_data_monitoring_cpr_akun_status');
            $table->dropIndex('idx_data_monitoring_cpr_date_range');
            $table->dropIndex('idx_data_monitoring_cpr_common_query');
        });
    }
};
