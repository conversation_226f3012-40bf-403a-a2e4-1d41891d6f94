@extends('templates.app')
@section('title', $title)
@push('style')
    <style>
        .text-overflow {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80%;
            white-space: normal;
            /* Membuat text wrap ke bawah */
            word-wrap: break-word;
            /* Memastikan kata panjang juga wrap */
        }

        /* .dataTables_paginate {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    display: none;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                } */
        display: none;
        }

        */ .wrap-text {
            word-wrap: break-word;
            /* Membungkus teks yang panjang */
            white-space: normal;
            /* Menyebabkan teks membungkus jika diperlukan */
        }

        .card-option {
            border: 2px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
            position: relative;
            padding: 1.5rem;
        }

        .card-option.active {
            border-color: #198754;
            background-color: #e9f7ef;
            box-shadow: 0 0 15px rgba(25, 135, 84, 0.3);
        }

        .card-option input[type="radio"] {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        /* .hidden-secure {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    display: none !important;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                } */
        display: none !important;
        }

        */ .select2-container .select2-selection--single {
            height: 38px !important;
            padding: 5px;
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
        }

        .shipping-option {
            border: 2px solid transparent;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .shipping-option.selected {
            border-color: #8a2be2;
            /* Warna ungu */
            background-color: #f3e8ff;
        }
    </style>
@endpush
@section('content')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
    <hr />
    <div class="card">
        <div class="card-body">
            @if (in_array(auth()->user()->level_user, [3]))
                <!-- Button Import -->
                <button id="btnImportOrder" class="btn btn-warning mb-2">
                    <i class="bx bx-import"></i>
                    Import {{ $title }}
                </button>
                <!-- Button Bulk Update -->
                <button type="button" class="btn btn-primary mb-2" id="openBulkUpdateModal" style="display: none;"
                    data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                    <i class="bx bx-edit"></i>
                    Bulk Update
                </button>
            @endif
            <div class="col-md-3 d-inline-block">
                <input type="text" id="dateRangePicker" class="form-control mb-2"
                    value="{{ date('Y-m-01') . ' to ' . date('Y-m-t') }}" placeholder="Select Date Range">
            </div>
            <div class="table-responsive">
                <table id="tabelOrder" class="table datatable table-striped" style="width:100%">
                    <thead>
                        <tr>
                            {{-- @if (Auth::user()->level_user == 5) --}}
                            <th><input type="checkbox" id="select-all"></th>
                            {{-- @endif --}}
                            <th>No</th>
                            @if (in_array(Auth::user()->level_user, [3, 5]))
                                <th>Aksi</th>
                            @endif
                            <th>Order ID</th>
                            <th>Nama</th>
                            <th>Tags</th>
                            <th>Kode Produk</th>
                            <th>Nama Produk</th>
                            <th>Variasi</th>
                            <th>Quantity</th>
                            <th>Kota</th>
                            <th>Shipping Info</th>
                            <th>Status Pembayaran</th>
                            <th>Status</th>
                            <th>No Resi</th>
                            <th>Ditugaskan untuk</th>
                            <th>Tanggal</th>
                            <th>Gross Revenue</th>
                            <th>Informasi Pembayaran</th>
                            @if (Auth::user()->level_user != 3)
                                <th>Meta</th>
                                <th>UTM Source</th>
                                <th>UTM Campaign</th>
                            @endif
                            <th>Follow UP</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <div id="modalContainer"></div>
    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">Preview Order Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="previewContent">
                    Loading preview...
                </div>
            </div>
        </div>
    </div>
    <!-- Modal Bulk Update -->
    <div class="modal fade" id="bulkUpdateModal" tabindex="-1" aria-labelledby="bulkUpdateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkUpdateModalLabel">Perbarui Secara Massal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <!-- Modal Body -->
                <div class="modal-body">
                    <form id="bulk-update-form">
                        <!-- Payment Status -->
                        <div class="row mb-3">
                            <label for="bulk-payment-status" class="col-sm-4 col-form-label">
                                Status Pembayaran:
                            </label>
                            <div class="col-sm-8">
                                <select id="bulk-payment-status" class="form-select" required>
                                    <option value="">-- Pilih Status Pembayaran --</option>
                                    @foreach ($paymentStatuses as $key => $status)
                                        <option value="{{ strtolower($key) }}">{{ ucfirst(strtolower($status)) }}</option>
                                    @endforeach
                                </select>

                                <small class="text-muted">Pilih status pembayaran untuk baris yang dipilih.</small>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="row mb-3">
                            <label for="bulk-status" class="col-sm-4 col-form-label">
                                Status:
                            </label>
                            <div class="col-sm-8">
                                <select id="bulk-status" class="form-select" required>
                                    <option value="">-- Pilih Status --</option>
                                    @foreach ($orderStatuses as $key => $status)
                                        <option value="{{ strtolower($key) }}">{{ ucfirst(strtolower($status)) }}</option>
                                    @endforeach
                                </select>
                                <small class="text-muted">Pilih status untuk baris yang dipilih.</small>
                            </div>
                        </div>

                        <!-- Payment Info -->
                        <div class="row mb-3">
                            <label for="bulk-payment-info" class="col-sm-4 col-form-label">
                                Informasi Pembayaran:
                            </label>
                            <div class="col-sm-8">
                                <select id="bulk-payment-info" class="form-select" required>
                                    <option value="">-- Pilih Payment Info --</option>
                                    @foreach ($paymentMethods as $key => $status)
                                        <option value="{{ strtolower($key) }}">{{ strtolower($status) }}</option>
                                    @endforeach
                                </select>
                                <small class="text-muted">Masukkan informasi pembayaran untuk baris yang dipilih.</small>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Modal Footer -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-success" id="apply-bulk-update">
                        Update
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script>
        $(document).ready(function() {
            flatpickr("#dateRangePicker", {
                mode: "range",
                dateFormat: "Y-m-d",
            });
            var date = $("#dateRangePicker").val();

            var table = $('#tabelOrder').DataTable({
                ajax: {
                    url: "{{ route('order.data') }}",
                    data: function(d) {
                        d.date = date;
                    },
                },
                processing: true,
                serverSide: true,
                stateSave: true,
                deferRender: true,
                "lengthMenu": [
                    [10, 25, 50, -1],
                    [10, 25, 50, "All"]
                ],
                "pageLength": -1,
                columns: [{
                        data: 'checkbox',
                        name: 'checkbox',
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    }, {
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    }, // Kolom nomor urut
                    @if (in_array(Auth::user()->level_user, [3, 5]))
                        {
                            data: 'action',
                            orderable: false,
                            searchable: false
                        }, // Kolom aksi
                    @endif // Kolom aksi
                    {
                        data: 'order_id',
                        name: 'order_id'
                    },
                    {
                        data: 'name',
                        name: 'name',
                        className: 'wrap-text'
                    },
                    {
                        data: 'tags',
                        name: 'tags',
                        className: 'wrap-text',
                        width: '150px'
                    },
                    {
                        data: 'product_code',
                        name: 'product_code'
                    },
                    {
                        data: 'product',
                        name: 'product',
                        className: 'wrap-text',
                        width: '250px'
                    },
                    {
                        data: 'variation',
                        name: 'variation',
                        className: 'wrap-text'
                    },
                    {
                        data: 'quantity',
                        name: 'quantity'
                    },
                    {
                        data: 'city',
                        name: 'city'
                    },
                    {
                        data: 'courier',
                        name: 'courier'
                    },
                    {
                        data: 'payment_status',
                        name: 'payment_status'
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'receipt_number',
                        name: 'receipt_number',
                        render: function(data, type, row) {
                            if (data) {
                                return data;
                            } else {
                                return '<span class="text-muted">-</span>';
                            }
                        }
                    },
                    {
                        data: 'handled_by',
                        name: 'handled_by'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        render: function(data, type, row) {
                            var date = new Date(data);
                            var options = {
                                year: 'numeric',
                                month: 'short',
                                day: '2-digit',
                                hour12: false
                            };
                            return date.toLocaleDateString('id-ID', options);
                        }
                    },
                    {
                        data: 'gross_revenue',
                        name: 'gross_revenue',
                        render: function(data, type, row) {
                            if (data) {
                                return 'Rp ' + number_format(data, 2, ',', '.');
                            }
                        }
                    },
                    {
                        data: 'payment_method',
                        name: 'payment_method'
                    },
                    @if (Auth::user()->level_user != 3)
                        {
                            data: 'ip_address',
                            name: 'ip_address'
                        }, {
                            data: 'utm_source',
                            name: 'utm_source'
                        }, {
                            data: 'utm_campaign',
                            name: 'utm_campaign'
                        }
                    @endif // Kolom aksi
                    {
                        data: 'follow_up',
                        name: 'follow_up',
                    },
                ],
            });

            $('#dateRangePicker').change(function() {
                if ($(this).val().split(' to ').length === 2) {
                    date = $(this).val();
                    table.draw();
                }
            });


            // Event: Klik tombol Tambah
            $('#btnTambahOrder').click(function() {
                $.get("{{ route('order.create') }}", function(data) {
                    $('#modalContainer').html(data);
                    $('#modalOrder').modal('show');

                    // === Gunakan Fungsi Reusable untuk Simpan Data ===
                    $(document).off('click', '#btnSimpanOrder').on('click', '#btnSimpanOrder',
                        function() {
                            simpanData(
                                '#formOrder',
                                '#modalOrder',
                                "{{ route('order.store') }}",
                                table
                            );
                        });
                });
            });

            // Event: Klik tombol Import
            $('#btnImportOrder').click(function() {
                $.get("{{ route('order.view-import') }}", function(data) {
                    $('#modalContainer').html(data);
                    $('#modalOrderImport').modal('show');

                    // === Gunakan Fungsi Reusable untuk Simpan Data ===
                    $(document).off('submit', '#orderImportForm').on('submit', '#orderImportForm',
                        function(e) {
                            e.preventDefault();

                            var formData = new FormData(this);
                            $(".progress").removeClass("d-none");
                            $(".progress-bar").css("width", "0%").text("0%");

                            $.ajax({
                                url: "{{ route('order.import') }}",
                                type: "POST",
                                data: formData,
                                processData: false,
                                contentType: false,
                                beforeSend: function() {
                                    $(".progress-bar").css("width", "20%").text(
                                        "20%");
                                },
                                xhr: function() {
                                    var xhr = new window.XMLHttpRequest();
                                    xhr.upload.addEventListener("progress",
                                        function(evt) {
                                            if (evt.lengthComputable) {
                                                var percentComplete = Math
                                                    .round((evt.loaded / evt
                                                        .total) * 100);
                                                $(".progress-bar").css("width",
                                                        percentComplete + "%")
                                                    .text(percentComplete +
                                                        "%");
                                            }
                                        }, false);
                                    return xhr;
                                },
                                success: function(response) {
                                    $(".progress-bar").css("width", "100%").text(
                                        "100%");
                                    $("#message").html(
                                        '<div class="alert alert-success">File berhasil diunggah dan sedang diproses.</div>'
                                    );
                                    setTimeout(function() {
                                        $('#modalOrderImport').modal(
                                            'hide');
                                        table.draw();
                                    }, 3000); // Tutup modal setelah 3 detik
                                },
                                error: function(xhr) {
                                    $(".progress-bar").css("width", "100%").text(
                                        "100%");
                                    $("#message").html(
                                        '<div class="alert alert-danger">Gagal mengunggah file: ' +
                                        xhr.responseJSON.message + '</div>');
                                }
                            });
                        });
                });
            });


            // Event: Klik tombol Edit
            $('#tabelOrder').on('click', '.btn-edit', function() {
                let id = $(this).data('id');
                let url = "{{ route('order.edit', ':id') }}".replace(':id', id);
                $.get(url, function(data) {
                    $('#modalContainer').html(data);
                    $('#modalOrder').modal('show');

                    // === Gunakan Fungsi Reusable untuk Simpan Data ===
                    $(document).off('click', '#btnSimpanOrder').on('click', '#btnSimpanOrder',
                        function() {
                            simpanData(
                                '#formOrder',
                                '#modalOrder',
                                "{{ route('order.update', ':id') }}".replace(':id', id),
                                table
                            );
                        });
                });
            });

            // Event: Klik tombol status order
            $('#tabelOrder').on('click', '.btn-status', function() {
                Swal.fire({
                    title: "Are you sure?",
                    text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, update it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        let id = $(this).data('id');
                        simpanData(
                            '',
                            '',
                            "{{ route('order.update-status', ':id') }}".replace(':id', id),
                            table
                        );
                    } else if (
                        /* Read more about handling dismissals below */
                        result.dismiss === Swal.DismissReason.cancel
                    ) {
                        Swal.fire({
                            title: "Cancelled",
                            text: "Status update was cancelled.",
                            icon: "error"
                        });
                    }
                });

            });

            // Event: Klik tombol payment status
            $('#tabelOrder').on('click', '.btn-payment-status', function() {
                Swal.fire({
                    title: "Are you sure?",
                    text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, update it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        let id = $(this).data('id');
                        simpanData(
                            '',
                            '',
                            "{{ route('order.update-payment-status', ':id') }}".replace(':id',
                                id),
                            table
                        );
                    } else if (
                        /* Read more about handling dismissals below */
                        result.dismiss === Swal.DismissReason.cancel
                    ) {
                        Swal.fire({
                            title: "Cancelled",
                            text: "Payment status update was cancelled.",
                            icon: "error"
                        });
                    }
                });
            });

            // Event: Klik tombol Hapus
            $('#tabelOrder').on('click', '.btn-delete', function() {
                let id = $(this).data('id');
                let url = "{{ route('order.destroy', ':id') }}".replace(':id', id);
                confirmDelete(url, table)
            });

            // Event: Klik tombol Create Resi
            $('#tabelOrder').on('click', '.btn-create-resi', function() {
                let id = $(this).data('id');
                $.get("{{ route('order.form-create-resi') }}", {
                    id: id
                }, function(data) {
                    $('#modalContainer').html(data);
                    $('#modalCreateResi').modal('show');

                    // === Gunakan Fungsi Reusable untuk Simpan Data ===
                    $(document).off('click', '#btnSimpanResi').on('click', '#btnSimpanResi',
                        function() {
                            simpanData(
                                '#formCreateResi',
                                '#modalCreateResi',
                                "{{ route('order.create-resi') }}",
                                table
                            );
                        });
                });
            });

            // Event: Klik tombol Create Satuan Detail Ongkir
            $('#tabelOrder').on('click', '.btn-create-satuan-ongkir', function() {
                let id = $(this).data('id');

                $.get("{{ route('order.form-create-satuan-ongkir') }}", {
                    id: id
                }, function(response) {
                    if (response.status === 'exists') {
                        loadPreviewToPrint(response.data);
                        return; // Stop execution
                    }

                    $('#modalContainer').html(response);
                    $('#modalCreateSatuanOngkir').modal('show');

                    $(document).off('click', '#btnSimpanSatuanOngkir').on('click',
                        '#btnSimpanSatuanOngkir',
                        function() {
                            simpanDataWithPromise(
                                '#formCreateSatuanOngkir',
                                '#modalCreateSatuanOngkir',
                                "{{ route('order.create-satuan-ongkir') }}",
                                table
                            ).then(response => {
                                console.log(response);
                                loadPreviewToPrint(response.data);
                            }).catch(err => {
                                console.log(err);
                            });
                        });
                });
            });

            // Event: Klik tombol Cetak Resi
            $('#tabelOrder').on('click', '.btn-print-resi', function() {
                let id = $(this).data('id');
                let url = "{{ route('order.cetak-resi') }}?id=" + id;
                // Open in a new window
                window.open(url, '_blank');
            });

            //load loadPreviewToPrint
            function loadPreviewToPrint(data) {
                let previewHtml = ""; // Deklarasi variabel di awal

                if (data.payment_method === "bank_transfer") {
                    previewHtml = `
                        <p><strong>Harga Satuan:</strong> Rp. ${data.harga_satuan}</p>
                        <p><strong>Total Biaya Kulak:</strong> Rp. ${data.total_biaya_kulak}</p>
                        <p><strong>Ongkir:</strong> Rp. ${data.ongkir}</p>
                        <p><strong>Harga Jual:</strong> Rp. ${data.harga_jual}</p>
                        <p><strong>Tag Konsumen:</strong> Rp. ${data.tag_konsumen}</p>
                        <p><strong>Fee Gudang:</strong> Rp. ${data.fee_gudang}</p>
                        <p><strong>Profit:</strong> Rp. ${data.profite}</p>
                    `;
                } else {
                    previewHtml = `
                        <p><strong>Harga Satuan:</strong> Rp. ${data.harga_satuan}</p>
                        <p><strong>Total Biaya Kulak:</strong> Rp. ${data.total_biaya_kulak}</p>
                        <p><strong>Ongkir:</strong> Rp. ${data.ongkir}</p>
                        <p><strong>Harga Jual:</strong> Rp. ${data.harga_jual}</p>
                        <p><strong>Biaya COD:</strong> Rp. ${data.biaya_cod}</p>
                        <p><strong>PPN 11%:</strong> Rp. ${data.ppn_11}</p>
                    `;
                }

                $('#previewContent').html(previewHtml); // Populate modal body
                $('#previewModal').modal('show'); // Show modal
            }

            // method modal resi
            $(document).on('shown.bs.modal', '#modalCreateResi', function() {
                $('textarea').each(function() {
                    $(this).val($(this).val().trim());
                });

                // // Aktifkan Select2 untuk pencarian desa/kecamatan
                $('#areaTujuan').select2({
                    placeholder: "Cari Desa/Kecamatan...",
                    allowClear: true,
                    dropdownParent: $('#areaTujuan').parent()
                });

                // package type



                // Fungsi aktifkan kartu
                function activateCard(selector) {
                    $(selector).closest('.row').find('.card-option').removeClass('active');
                    $(selector).addClass('active');
                    $(selector).find('input[type="radio"]').prop('checked', true);
                }

                // Event untuk Pick Up / Drop Off
                $('#pickUpCard, #dropOffCard').on('click', function() {
                    activateCard(this);
                });

                // Event untuk COD / Non-COD
                $('#codCard').on('click', function() {
                    activateCard(this);
                    $('#customCodCard').fadeIn().removeClass('hidden-secure');
                });

                $('#nonCodCard').on('click', function() {
                    activateCard(this);
                    $('#customCodCard').fadeOut().addClass('hidden-secure');
                });

                // Inisialisasi tampilan saat load
                if ($('#nonCod').is(':checked')) $('#customCodCard').addClass('hidden-secure');

                // dropdown wilayah
                // Inisialisasi Dropdown Provinsi (Root)
                $('.select2').select2({
                    dropdownParent: $('.select2').parent()
                })
                initSelect2('#provinsi', 'Pilih Provinsi', "{{ route('wilayah.provinsi') }}", '#kabupaten',
                    'id', 'provinsi_name');

                // Event Change Provinsi -> Load Kabupaten
                $('#provinsi').on('change', function() {
                    $('#kabupaten, #kecamatan, #desa').empty().trigger('change');
                    $('#form-kabupaten, #form-kecamatan, #form-desa').hide();

                    const provinsiID = $(this).val();
                    if (provinsiID) {
                        initSelect2('#kabupaten', 'Pilih Kabupaten',
                            '{{ route('wilayah.kabupaten', ':id') }}'.replace(':id',
                                provinsiID), '#kecamatan', 'id', 'kabupaten_name');
                        $('#form-kabupaten').show();

                    }
                });

                // Event Change Kabupaten -> Load Kecamatan
                $('#kabupaten').on('change', function() {
                    $('#kecamatan, #desa').empty().trigger('change');
                    $('#form-kecamatan, #form-desa').hide();

                    const kabupatenID = $(this).val();
                    if (kabupatenID) {
                        initSelect2('#kecamatan', 'Pilih Kecamatan',
                            '{{ route('wilayah.kecamatan', ':id') }}'.replace(':id',
                                kabupatenID), '#desa', 'id', 'kecamatan_name');
                    }
                });

                // Event Change Kecamatan -> Load Desa
                $('#kecamatan').on('change', function() {
                    $('#desa').empty().trigger('change');
                    $('#form-desa').hide();

                    const kecamatanID = $(this).val();
                    if (kecamatanID) {
                        initSelect2('#desa', 'Pilih Desa',
                            '{{ route('wilayah.kelurahan', ':id') }}'.replace(':id',
                                kecamatanID), null, 'id', 'kelurahan_name');
                    }
                });

                $('#schedules').select2({
                    placeholder: 'Pilih Jadwal Pengiriman',
                    allowClear: true,
                    dropdownParent: $('#schedules').parent(),
                    ajax: {
                        url: "{{ route('kiriminaja.schedules.getSchedules') }}",
                        type: 'GET',
                        dataType: 'json',
                        processResults: function(data) {
                            return {
                                results: $.map(data.schedules, function(item) {
                                    return {
                                        id: item['clock'],
                                        text: item['clock']
                                    };
                                })
                            };
                        }
                    }
                });

                $('#nilaiBarang').on('keyup', function(e) {
                    this.value = formatRupiah(this.value);
                });

                $('#beratBarang').on('input', function() {
                    $(this).val($(this).val().replace(/[^0-9,]/g, '')); // Hanya Angka dan Koma
                });

                $('#panjangBarang').on('input', function() {
                    $(this).val($(this).val().replace(/[^0-9,]/g, '')); // Hanya Angka dan Koma
                });

                $('#lebarBarang').on('input', function() {
                    $(this).val($(this).val().replace(/[^0-9,]/g, '')); // Hanya Angka dan Koma
                });

                $('#tinggiBarang').on('input', function() {
                    $(this).val($(this).val().replace(/[^0-9,]/g, '')); // Hanya Angka dan Koma
                });

                $('#jumlahBarang').on('input', function() {
                    $(this).val($(this).val().replace(/[^0-9]/g, '')); // Hanya Angka
                });

                // Fungsi untuk format rupiah
                function formatRupiah(angka, prefix = "Rp. ", allowNegative = false) {
                    let number_string = angka.replace(/[^0-9,-]/g, "").toString(),
                        isNegative = allowNegative && number_string.startsWith('-'),
                        split = number_string.replace('-', '').split(','),
                        sisa = split[0].length % 3,
                        rupiah = split[0].substr(0, sisa),
                        ribuan = split[0].substr(sisa).match(/\d{3}/gi);

                    if (ribuan) {
                        let separator = sisa ? "." : "";
                        rupiah += separator + ribuan.join(".");
                    }

                    rupiah = split[1] !== undefined ? rupiah + "," + split[1] : rupiah;

                    // Hilangkan prefix jika angka kosong
                    return rupiah ? (isNegative ? "- " : "") + prefix + rupiah : "";
                }
                // Fungsi dinamis untuk cek semua field pada form yang diberikan
                function checkAllFilled(form) {
                    let allFilled = true;
                    $(form).find('input[required], select[required], textarea[required]').each(function() {
                        // console.log($(this).val());
                        // console.log($(this));
                        if ($(this).hasClass('select2-hidden-accessible')) {
                            if ($(this).val() === null || $(this).val().trim() === '') {
                                allFilled = false;
                                return false; // Break loop
                            }
                        }
                        // Jika elemen biasa
                        else if ($(this).val().trim() === '') {
                            allFilled = false;
                            return false; // Break loop
                        }
                    });
                    return allFilled;
                }

                // Fungsi dinamis untuk trigger AJAX
                function triggerAjax(form) {
                    if (checkAllFilled(form)) {
                        $.ajax({
                            url: "{{ route('kiriminaja.kurir.getCurier') }}", // Ganti dengan URL route Laravel
                            type: 'post',
                            // data: new FormData($(form)[0]), // Mengirimkan data form
                            data: new FormData($('#formCreateResi')[0]), // Mengirimkan data form
                            // data: function() {
                            //     var formData = new FormData($('#formCreateResi')[0]);
                            //     // $('#formCreateResi').find('input, select, textarea').each(function() {
                            //     //     var id = $(this).attr('id');
                            //     //     if (id) {
                            //     //         formData.append(id, $(this).val());
                            //     //     }
                            //     // });
                            //     return formData;
                            // },
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                $('#shipping-options').html('');
                                response.results.forEach(item => {
                                    const text = item.service;
                                    switch (true) {
                                        case text.toLowerCase().includes("jne"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/jne.png";
                                            break;
                                        case text.toLowerCase().includes("jnt"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/jnt.png";
                                            break;
                                        case text.toLowerCase().includes("sicepat"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/sicepat.png";
                                            break;
                                        case text.toLowerCase().includes("ninja"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/ninja.png";
                                            break;
                                        case text.toLowerCase().includes("tiki"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes(
                                            "posindonesia"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("paxel"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("gosend"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("rpx"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("borzo"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("indah"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("lion"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes(
                                            "grab_express"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("jtcargo"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("sentral"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("anteraja"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("jtl"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("ncs"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("ncs"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("sap"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        case text.toLowerCase().includes("idx"):
                                            var url_img =
                                                "https://kaj-prd-dshbd-v2-assets.kiriminaja.com/public/assets/courier-logo/tiki.png";
                                            break;
                                        default:
                                            console.log("Kurir tidak ditemukan.");
                                    }
                                    // <input type="radio" name="shipping_option"
                                    //        data-service="${item.service}"
                                    //        data-service_type="${item.service_type}"
                                    //        data-shipping_cost="${item.shipping_cost}"
                                    //        class="d-none"
                                    // >
                                    const card = `
                                        <label class="shipping-card w-100 d-block">
                                          <input type="radio"
                                            data-service="${item.service}"
                                            data-service_type="${item.service_type}"
                                            data-shipping_cost="${item.cost}"
                                            class="d-none"
                                          >
                                          <div class="card mb-2 p-2 border-0 shadow-sm rounded-3 shipping-option">
                                            <div class="card-body d-flex justify-content-between align-items-center">
                                              <div class="d-flex align-items-center">
                                                <img src="${url_img}" alt="${item.service_name}" class="me-3" style="width: 50px; height: 30px;">
                                                <div>
                                                  <h6 class="mb-1">${item.service_name}</h6>
                                                  <p class="text-primary fw-bold mb-0">Rp. ${number_format(item.cost)}</p>
                                                </div>
                                              </div>
                                              <div class="text-end">
                                                <p class="mb-1">${item.etd} Hari</p>
                                                ${item.etd ? '<span class="badge bg-success">Rekomendasi</span>' : ''}
                                              </div>
                                            </div>
                                          </div>
                                        </label>
                                      `;
                                    $('#shipping-options').append(card);
                                });
                            },
                            error: function(xhr) {
                                console.log('Gagal:', xhr.responseText);
                            }
                        });
                    }
                }
                // Event listener dinamis untuk semua form dengan class .ajaxForm
                $('.ajaxForm input, .ajaxForm select, .ajaxForm textarea').on('input change', function() {
                    // console.log('test ajax');
                    let form = $(this).closest(
                        'form'); // Cari form terdekat dari elemen yang berubah
                    triggerAjax(form);
                });

                $(document).on("click", ".shipping-card", function() {
                    $(".shipping-option").removeClass("selected");
                    $(this).find(".shipping-option").addClass("selected");
                    $(this).find("input").prop("checked", true);

                    // bind data
                    let service = $(this).find("input").data("service");
                    let service_type = $(this).find("input").data("service_type");
                    let shipping_cost = $(this).find("input").data("shipping_cost");
                    $("#service").val(service);
                    $("#service_type").val(service_type);
                    $("#shipping_cost").val(shipping_cost);
                });

                $(document).off('click', '#btnSimpanResi').on('click', '#btnSimpanResi', function() {
                    simpanData(
                        '#formCreateResi',
                        '#modalCreateResi',
                        "{{ route('kiriminaja.order.store') }}",
                        table
                    );
                });
            });

            // Inisialisasi array untuk menyimpan baris yang dipilih
            let selectedRows = [];

            // Seleksi semua row
            $('#select-all').on('click', function() {
                $('.row-selector').prop('checked', this.checked);
                updateSelectedRows();
            });

            // Seleksi satu row (menggunakan event delegation)
            $(document).on('change', '.row-selector', function() {
                updateSelectedRows();
            });

            // Fungsi untuk memperbarui baris yang dipilih
            function updateSelectedRows() {
                selectedRows = [];
                $('.row-selector:checked').each(function() {
                    selectedRows.push($(this).data('id'));
                });

                // Tampilkan atau sembunyikan tombol modal bulk update
                if (selectedRows.length > 1) {
                    $('#openBulkUpdateModal').show();
                } else {
                    $('#openBulkUpdateModal').hide();
                }
            }

            // Handle bulk update
            $('#apply-bulk-update').on('click', function() {
                const paymentStatus = $('#bulk-payment-status').val();
                const status = $('#bulk-status').val();
                const paymentMethod = $('#bulk-payment-info').val();

                if (selectedRows.length === 0) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Peringatan',
                        text: 'Silakan pilih data terlebih dahulu.',
                        timer: 2000
                    })
                    return;
                }

                //Kirim AJAX request ke server
                $.ajax({
                    url: "{{ route('order.bulk-update') }}",
                    method: 'POST',
                    data: {
                        ids: selectedRows,
                        payment_status: paymentStatus,
                        status: status,
                        payment_method: paymentMethod
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: response.message,
                                timer: 2000,
                            })

                            setTimeout(() => {
                                $('#bulkUpdateModal').modal('hide');
                                $('#openBulkUpdateModal').hide();

                                table.ajax.reload();
                            }, 1500);
                        }
                    },
                    error: function() {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Terjadi kesalahan, coba lagi.',
                            timer: 2000,
                        })
                    },
                });
            });

            // Event: Klik tombol Create Resi
            $('#tabelOrder').on('click', '.btn-upload-bukti-pembayaran', function() {
                let id = $(this).data('id');
                $('#modalContainer').html(`
                    <div class="modal fade" id="modalUploadBuktiPembayaran" tabindex="-1" aria-labelledby="modalUploadBuktiPembayaranLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="modalUploadBuktiPembayaranLabel">Upload Bukti Pembayaran</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="formUploadBuktiPembayaran">
                                        <div class="mb-3">
                                            <label for="buktiPembayaran" class="form-label">Pilih File</label>
                                            <input type="file" class="form-control" id="buktiPembayaran" name="bukti_pembayaran" required>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <button type="button" class="btn btn-primary" id="btnUploadBuktiPembayaran">Upload</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `);

                $('#modalUploadBuktiPembayaran').modal('show');

                $(document).off('click', '#btnUploadBuktiPembayaran').on('click',
                    '#btnUploadBuktiPembayaran',
                    function() {
                        let formData = new FormData($('#formUploadBuktiPembayaran')[0]);
                        formData.append('id', id);
                        $.ajax({
                            url: "{{ route('order.upload-bukti-pembayaran') }}",
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success',
                                        text: response.message,
                                        timer: 2000,
                                    });

                                    setTimeout(() => {
                                        $('#modalUploadBuktiPembayaran').modal(
                                            'hide');
                                        table.ajax.reload();
                                    }, 1500);
                                }
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: xhr.responseJSON.message ||
                                        'Terjadi kesalahan, coba lagi.',
                                    timer: 2000,
                                });
                            }
                        });
                    });
            });

            $('#tabelOrder').on('click', '.btn-lihat-bukti-pembayaran', function() {
                let id = $(this).data('id');
                $('#modalContainer').html(`
                    <div class="modal fade" id="modalLihatBuktiPembayaran" tabindex="-1" aria-labelledby="modalLihatBuktiPembayaranLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="modalLihatBuktiPembayaranLabel">Lihat Bukti Pembayaran</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <img id="buktiPembayaranImage" src="" alt="Bukti Pembayaran" class="img-fluid rounded shadow">
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `);

                // Set the image source dynamically
                const buktiPembayaranUrl = $(this).data('bukti-pembayaran');
                $('#buktiPembayaranImage').attr('src', buktiPembayaranUrl);

                $('#modalLihatBuktiPembayaran').modal('show');

                $('#modalUploadBuktiPembayaran').modal('show');
            });
        });

        // Event: Klik tombol payment status
        $('#tabelOrder').on('click', '.btn-followup-welcome', function() {
            Swal.fire({
                title: "Are you sure?",
                text: "Kamu akan Follow Up!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, follow up!"
            }).then((result) => {
                if (result.isConfirmed) {
                    let id = $(this).data('id');
                    $.ajax({
                        url: "{{ route('order.follow-up') }}",
                        type: "POST",
                        data: {
                            id: id
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message,
                                    timer: 2000,
                                });

                                setTimeout(() => {
                                    table.ajax.reload();
                                }, 1500);
                            }
                        },
                        error: function(xhr) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: xhr.responseJSON.message ||
                                    'Terjadi kesalahan, coba lagi.',
                                timer: 2000,
                            });
                        }
                    });
                } else if (
                    /* Read more about handling dismissals below */
                    result.dismiss === Swal.DismissReason.cancel
                ) {
                    Swal.fire({
                        title: "Cancelled",
                        text: "Payment status update was cancelled.",
                        icon: "error"
                    });
                }
            });
        });
    </script>
@endpush
