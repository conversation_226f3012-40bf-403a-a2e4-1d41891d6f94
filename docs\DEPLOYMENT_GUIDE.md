# Deployment Guide - Enhanced Monitoring CPR

## 🚀 Quick Deployment Checklist

### ✅ Pre-deployment
- [ ] Database migrations completed
- [ ] Queue system configured
- [ ] Facebook API credentials verified
- [ ] Scheduled jobs tested
- [ ] Cache system working

### ✅ Deployment Steps
- [ ] Deploy code to server
- [ ] Run migrations
- [ ] Clear caches
- [ ] Restart queue workers
- [ ] Test auto-sync functionality

### ✅ Post-deployment
- [ ] Monitor logs for errors
- [ ] Verify scheduled jobs running
- [ ] Test real-time features
- [ ] Check performance metrics

## 🛠️ Step-by-Step Deployment

### 1. Code Deployment
```bash
# Pull latest code
git pull origin main

# Install/update dependencies
composer install --optimize-autoloader --no-dev

# Clear and cache config
php artisan config:clear
php artisan config:cache

# Clear and cache routes
php artisan route:clear
php artisan route:cache

# Clear and cache views
php artisan view:clear
php artisan view:cache
```

### 2. Database Setup
```bash
# Run migrations
php artisan migrate --force

# Verify indexes
php artisan migrate:status

# Check table structure
php artisan tinker
>>> Schema::hasTable('data_monitoring_cpr')
>>> Schema::getColumnListing('data_monitoring_cpr')
```

### 3. Queue Configuration
```bash
# Create queue tables if not exists
php artisan queue:table
php artisan migrate

# Restart queue workers
sudo supervisorctl restart laravel-worker:*

# Or if using systemd
sudo systemctl restart laravel-queue

# Test queue
php artisan queue:work --once
```

### 4. Scheduled Jobs Setup
```bash
# Add to crontab
crontab -e

# Add this line:
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1

# Test scheduled commands
php artisan schedule:list
php artisan cpr:auto-sync --force
```

### 5. Cache Configuration
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Warm up caches
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🧪 Testing Procedures

### 1. Basic Functionality Test
```bash
# Test database connection
php artisan tinker
>>> DB::connection()->getPdo()

# Test Facebook API connection
>>> app(\App\Services\Facebook\FacebookAdService::class)->testConnection()

# Test auto-sync command
php artisan cpr:auto-sync --force
```

### 2. Real-time Features Test
1. **Open monitoring page** in browser
2. **Check auto-refresh** is working (countdown timer)
3. **Test manual refresh** button
4. **Test sync data** button
5. **Verify notifications** appear
6. **Check status indicators** update

### 3. Performance Test
```bash
# Monitor database queries
php artisan telescope:install # if using Telescope

# Check memory usage
php artisan tinker
>>> memory_get_usage(true)

# Monitor queue performance
php artisan queue:monitor
```

### 4. Load Testing
```bash
# Install siege for load testing
sudo apt-get install siege

# Test monitoring page
siege -c 10 -t 30s http://your-domain.com/ads/monitoring-cpr

# Test API endpoints
siege -c 5 -t 15s http://your-domain.com/ads/monitoring-cpr/data
```

## 📊 Monitoring and Alerts

### 1. Log Monitoring
```bash
# Monitor auto-sync logs
tail -f storage/logs/cpr-auto-sync.log

# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Monitor queue logs
tail -f storage/logs/queue.log
```

### 2. Performance Monitoring
```bash
# Check disk usage
df -h

# Check memory usage
free -m

# Check CPU usage
top

# Check database performance
mysql -u root -p -e "SHOW PROCESSLIST;"
```

### 3. Application Health Checks
```bash
# Create health check endpoint
# Add to routes/web.php:
Route::get('/health', function() {
    return response()->json([
        'status' => 'ok',
        'database' => DB::connection()->getPdo() ? 'connected' : 'disconnected',
        'cache' => Cache::store()->getStore() ? 'working' : 'failed',
        'queue' => 'working', // Add queue health check
        'timestamp' => now()
    ]);
});
```

### 4. Automated Monitoring Script
```bash
#!/bin/bash
# monitoring.sh

# Check if queue workers are running
if ! pgrep -f "queue:work" > /dev/null; then
    echo "Queue workers not running!"
    # Restart queue workers
    sudo supervisorctl restart laravel-worker:*
fi

# Check if auto-sync is working
LAST_SYNC=$(php artisan tinker --execute="echo Cache::get('cpr_last_auto_sync');")
if [ -z "$LAST_SYNC" ]; then
    echo "Auto-sync not working!"
    # Trigger manual sync
    php artisan cpr:auto-sync --force --queue
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is ${DISK_USAGE}%"
    # Clean up logs
    find storage/logs -name "*.log" -mtime +7 -delete
fi
```

## 🔧 Configuration Files

### 1. Supervisor Configuration
```ini
; /etc/supervisor/conf.d/laravel-worker.conf
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
stopwaitsecs=3600
```

### 2. Nginx Configuration
```nginx
# Add to your nginx site config
location /ads/monitoring-cpr {
    try_files $uri $uri/ /index.php?$query_string;
    
    # Enable real-time features
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Increase timeout for sync operations
    proxy_read_timeout 300;
    proxy_connect_timeout 300;
    proxy_send_timeout 300;
}
```

### 3. Environment Variables
```env
# Add to .env file

# Queue Configuration
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database

# Cache Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# Facebook API Configuration
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_ACCESS_TOKEN=your_access_token

# Monitoring Configuration
CPR_AUTO_SYNC_ENABLED=true
CPR_SYNC_INTERVAL=10
CPR_MAX_RETRIES=3
```

## 🚨 Troubleshooting

### Common Deployment Issues

1. **Migration Errors**
```bash
# Check migration status
php artisan migrate:status

# Rollback if needed
php artisan migrate:rollback

# Re-run migrations
php artisan migrate --force
```

2. **Queue Not Working**
```bash
# Check queue configuration
php artisan config:show queue

# Restart queue workers
sudo supervisorctl restart laravel-worker:*

# Check failed jobs
php artisan queue:failed
```

3. **Permission Issues**
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/

# Fix cache permissions
sudo chown -R www-data:www-data bootstrap/cache/
sudo chmod -R 775 bootstrap/cache/
```

4. **Facebook API Issues**
```bash
# Test API connection
php artisan tinker
>>> app(\App\Services\Facebook\FacebookAdService::class)->testConnection()

# Check API credentials
>>> config('facebook')
```

### Performance Issues

1. **Slow Database Queries**
```sql
-- Check slow queries
SHOW PROCESSLIST;

-- Analyze table
ANALYZE TABLE data_monitoring_cpr;

-- Check indexes
SHOW INDEX FROM data_monitoring_cpr;
```

2. **High Memory Usage**
```bash
# Monitor memory usage
php artisan tinker
>>> memory_get_usage(true)

# Optimize autoloader
composer dump-autoload --optimize
```

3. **Cache Issues**
```bash
# Clear all caches
php artisan optimize:clear

# Rebuild caches
php artisan optimize
```

## 📞 Emergency Procedures

### 1. Rollback Deployment
```bash
# Rollback to previous version
git checkout previous-stable-tag

# Rollback migrations if needed
php artisan migrate:rollback --step=1

# Clear caches
php artisan optimize:clear
```

### 2. Emergency Maintenance
```bash
# Put application in maintenance mode
php artisan down --message="Emergency maintenance"

# Perform fixes
# ...

# Bring application back up
php artisan up
```

### 3. Data Recovery
```bash
# Backup current data
mysqldump -u root -p database_name data_monitoring_cpr > backup.sql

# Restore from backup if needed
mysql -u root -p database_name < backup.sql
```

## ✅ Post-Deployment Verification

### 1. Functional Tests
- [ ] Login to monitoring page
- [ ] Select account and verify data loads
- [ ] Test auto-refresh functionality
- [ ] Test manual sync
- [ ] Verify notifications work
- [ ] Check mobile responsiveness

### 2. Performance Tests
- [ ] Page load time < 3 seconds
- [ ] API response time < 1 second
- [ ] Auto-refresh works smoothly
- [ ] No memory leaks after 1 hour

### 3. Integration Tests
- [ ] Facebook API connection working
- [ ] Database queries optimized
- [ ] Queue processing normally
- [ ] Scheduled jobs running
- [ ] Logs being written correctly

## 📋 Maintenance Schedule

### Daily
- [ ] Check error logs
- [ ] Monitor queue status
- [ ] Verify auto-sync working

### Weekly
- [ ] Review performance metrics
- [ ] Clean up old logs
- [ ] Check disk space
- [ ] Update dependencies if needed

### Monthly
- [ ] Full system backup
- [ ] Performance optimization review
- [ ] Security updates
- [ ] Documentation updates
