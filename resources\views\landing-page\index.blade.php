@extends('templates.app')

@section('title', $title)

@section('content')
    <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
    <hr />
    <div class="card">
        <div class="card-body">
            <div class="col-md-6">
                <div class="action-container">
                    <button type="button" id="btnTambahMateriIklan" class="btn btn-primary mb-2" data-bs-toggle="modal" data-bs-target="#createLandingPageModal">
                        <i class="bx bx-group"></i>
                        Tambah {{ $title }}
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table id="tabelLandingPage" class="table datatable table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>NO</th>
                            <th>NAMA LANDING PAGE</th>
                            <th>VISITORS</th>
                            <th>STATUS</th>
                            <th>AKSI</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal untuk menampilkan detail produk -->
    <div class="modal fade" id="productsModal" tabindex="-1" aria-labelledby="productsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productsModalLabel">
                        <i class="bx bx-package me-2"></i>Detail Produk Landing Page
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6 id="landingPageName" class="mb-3"></h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Section</th>
                                    <th>Nama Produk</th>
                                    <th>Harga</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Data akan diisi oleh JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal untuk membuat landing page baru -->
    <div class="modal fade" id="createLandingPageModal" tabindex="-1" aria-labelledby="createLandingPageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createLandingPageModalLabel">
                        <i class="bx bx-plus-circle me-2"></i>Tambah Baru
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createLandingPageForm">
                        <div class="row">
                            <!-- Left Column - Product Selection -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title mb-3">
                                            <i class="bx bx-package me-2"></i>Pilih Produk
                                        </h6>
                                        <div class="mb-3">
                                            <select class="form-select" id="product_id" name="product_id" required>
                                                <option value="">Pilih</option>
                                                <!-- Options will be populated via AJAX -->
                                            </select>
                                        </div>

                                        <h6 class="card-title mb-3 mt-4">
                                            <i class="bx bx-cog me-2"></i>Facebook Pixel - IMPLEMENTASI SEDERHANA
                                            <span class="badge bg-success ms-2">Manual</span>
                                        </h6>

                                        <!-- Pixel Selection/Creation -->
                                        <div class="mb-3">
                                            <label class="form-label">Facebook Pixel ID</label>
                                            <div class="input-group">
                                                <select class="form-select" id="facebook_pixel_id" name="facebook_pixel_id">
                                                    <option value="">Pilih Pixel ID (Opsional)</option>
                                                    <option value="create_new" class="text-primary fw-bold">+ Buat Pixel Baru</option>
                                                    <!-- Options will be populated via AJAX -->
                                                </select>
                                                <button class="btn btn-outline-primary" type="button" id="refreshPixelBtn" title="Refresh List">
                                                    <i class="bx bx-refresh"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Create New Pixel Form (Hidden by default) -->
                                        <div id="createPixelForm" class="mb-3" style="display: none;">
                                            <div class="card border-primary">
                                                <div class="card-header bg-primary text-white py-2">
                                                    <h6 class="mb-0 text-white">
                                                        <i class="bx bx-plus-circle me-2 text-white"></i>
                                                        Buat Facebook Pixel Baru
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-2">
                                                            <label class="form-label">Nama Pixel <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="new_pixel_name" placeholder="Contoh: Main Business Pixel">
                                                        </div>
                                                        <div class="col-md-6 mb-2">
                                                            <label class="form-label">Pixel ID <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="new_pixel_id" placeholder="Contoh: 123456789012345">
                                                        </div>
                                                        <div class="col-12 mb-2">
                                                            <label class="form-label">Deskripsi</label>
                                                            <input type="text" class="form-control" id="new_pixel_description" placeholder="Deskripsi pixel (opsional)">
                                                        </div>
                                                        <div class="col-12">
                                                            <button type="button" class="btn btn-primary btn-sm" id="saveNewPixelBtn">
                                                                <i class="bx bx-save me-1"></i>Simpan Pixel
                                                            </button>
                                                            <button type="button" class="btn btn-secondary btn-sm ms-2" id="cancelNewPixelBtn">
                                                                <i class="bx bx-x me-1"></i>Batal
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-text">
                                            <strong>📊 Tracking Otomatis:</strong><br>
                                            • <span class="badge bg-primary">ViewContent</span> - Saat halaman dimuat<br>
                                            • <span class="badge bg-success">Purchase</span> - Saat form di-submit<br>
                                            <small class="text-muted">Tidak perlu konfigurasi tambahan!</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Pixel Information -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title mb-3">
                                            <i class="bx bx-info-circle me-2"></i>Informasi Facebook Pixel
                                            <span class="badge bg-success ms-2">Sederhana</span>
                                        </h6>

                                        <div class="alert alert-info">
                                            <h6><i class="bx bx-check-circle me-2"></i>Implementasi Manual & Sederhana</h6>
                                            <p class="mb-2">Pixel akan otomatis melacak 2 event penting:</p>
                                            <ul class="mb-0">
                                                <li><strong>ViewContent:</strong> Ketika pengunjung membuka landing page</li>
                                                <li><strong>Purchase:</strong> Ketika pengunjung submit form pemesanan</li>
                                            </ul>
                                        </div>

                                        <div class="alert alert-success">
                                            <h6><i class="bx bx-star me-2"></i>Data yang Dikirim</h6>
                                            <ul class="mb-0">
                                                <li>Nama produk</li>
                                                <li>ID produk</li>
                                                <li>Harga produk</li>
                                                <li>Currency: IDR</li>
                                            </ul>
                                        </div>

                                        <div class="alert alert-warning">
                                            <h6><i class="bx bx-bulb me-2"></i>Cara Testing</h6>
                                            <ol class="mb-0">
                                                <li>Install <strong>Facebook Pixel Helper</strong> (Chrome Extension)</li>
                                                <li>Buka landing page yang sudah dibuat</li>
                                                <li>Cek apakah event <strong>ViewContent</strong> terdeteksi</li>
                                                <li>Submit form dan cek event <strong>Purchase</strong></li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="tambahkanBtn">Tambahkan</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js"></script>
    <script>
        $(document).ready(function() {
            // Load data when modal is shown
            $('#createLandingPageModal').on('show.bs.modal', function() {
                loadProducts();
                loadFacebookPixels();
            });

            // Handle pixel selection change
            $('#facebook_pixel_id').on('change', function() {
                const selectedValue = $(this).val();
                if (selectedValue === 'create_new') {
                    $('#createPixelForm').slideDown();
                } else {
                    $('#createPixelForm').slideUp();
                    clearNewPixelForm();
                }
            });

            // Handle refresh pixel button
            $('#refreshPixelBtn').on('click', function() {
                loadFacebookPixels();
            });

            // Handle cancel new pixel
            $('#cancelNewPixelBtn').on('click', function() {
                $('#facebook_pixel_id').val('').trigger('change');
                $('#createPixelForm').slideUp();
                clearNewPixelForm();
            });

            // Handle save new pixel
            $('#saveNewPixelBtn').on('click', function() {
                saveNewPixel();
            });

            // Handle Tambahkan button click
            $('#tambahkanBtn').on('click', function() {
                const productId = $('#product_id').val();
                const pixelId = $('#facebook_pixel_id').val();

                if (!productId) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Peringatan!',
                        text: 'Silakan pilih produk terlebih dahulu.',
                        confirmButtonColor: '#696cff'
                    });
                    return;
                }

                // Collect form data - IMPLEMENTASI SEDERHANA
                const formData = {
                    product_id: productId,
                    facebook_pixel_id: pixelId || null,
                    // Pixel events otomatis: ViewContent & Purchase
                    pixel_events: ['ViewContent', 'Purchase'],
                    // Tidak perlu parameter rumit - data diambil dari produk
                    pixel_event_parameters: {}
                };

                // Store data in sessionStorage and redirect to builder
                sessionStorage.setItem('landingPageConfig', JSON.stringify(formData));
                window.location.href = '{{ route('landingPage.builder') }}';
            });

            // Initialize DataTable
            $('#tabelLandingPage').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{{ route('landingPage.data') }}',
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'visitors',
                        name: 'visitors'
                    },
                    {
                        data: 'status',
                        name: 'status',
                        className: 'text-center',
                        orderable: false
                    },
                    {
                        data: 'action',
                        name: 'action',
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [1, 'desc']
                ]
            });
        });

        // Delete function
        function deleteLandingPage(id) {
            Swal.fire({
                title: 'Yakin ingin menghapus?',
                text: 'Landing page yang dihapus tidak dapat dikembalikan!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '{{ route('landingPage.destroy', ':id') }}'.replace(':id', id),
                        method: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Berhasil!',
                                    text: response.message,
                                    confirmButtonColor: '#696cff'
                                });
                                $('#tabelLandingPage').DataTable().ajax.reload();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Gagal!',
                                    text: response.message,
                                    confirmButtonColor: '#696cff'
                                });
                            }
                        },
                        error: function(xhr) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: 'Terjadi kesalahan saat menghapus landing page.',
                                confirmButtonColor: '#696cff'
                            });
                        }
                    });
                }
            });
        }

        // Function to show products modal
        function showProductsModal(landingPageId, landingPageName, products) {
            // Set landing page name
            document.getElementById('landingPageName').textContent = 'Landing Page: ' + landingPageName;

            // Clear existing table body
            const tableBody = document.getElementById('productsTableBody');
            tableBody.innerHTML = '';

            // Populate table with products
            if (products && products.length > 0) {
                products.forEach(function(product) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            <span class="badge bg-primary">${product.section}</span>
                        </td>
                        <td>${product.name}</td>
                        <td><strong>${product.price}</strong></td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="3" class="text-center text-muted">
                        <i class="bx bx-info-circle me-2"></i>Tidak ada produk yang dipilih
                    </td>
                `;
                tableBody.appendChild(row);
            }

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('productsModal'));
            modal.show();
        }

        // Function to load products
        function loadProducts() {
            $.ajax({
                url: '{{ route('landingPage.getProducts') }}',
                method: 'GET',
                success: function(response) {
                    const productSelect = $('#product_id');
                    productSelect.empty();
                    productSelect.append('<option value="">Pilih</option>');

                    if (response.success && response.data) {
                        response.data.forEach(function(product) {
                            const price = product.discount_price || product.regular_price;
                            const formattedPrice = new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0
                            }).format(price);

                            productSelect.append(
                                `<option value="${product.id}">${product.name} - ${formattedPrice}</option>`
                            );
                        });
                    }
                },
                error: function(xhr) {
                    console.error('Error loading products:', xhr);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal memuat data produk.',
                        confirmButtonColor: '#696cff'
                    });
                }
            });
        }

        // Function to load Facebook pixels
        function loadFacebookPixels() {
            $.ajax({
                url: '{{ route('facebookPixel.getPixels') }}',
                method: 'GET',
                success: function(response) {
                    const pixelSelect = $('#facebook_pixel_id');
                    const currentValue = pixelSelect.val();

                    pixelSelect.empty();
                    pixelSelect.append('<option value="">Pilih Pixel ID (Opsional)</option>');
                    pixelSelect.append('<option value="create_new" class="text-primary fw-bold">+ Buat Pixel Baru</option>');

                    if (response.success && response.data) {
                        response.data.forEach(function(pixel) {
                            pixelSelect.append(
                                `<option value="${pixel.id}">${pixel.name} (${pixel.pixel_id})</option>`
                            );
                        });
                    }

                    // Restore previous selection if it wasn't create_new
                    if (currentValue && currentValue !== 'create_new') {
                        pixelSelect.val(currentValue);
                    }
                },
                error: function(xhr) {
                    console.error('Error loading pixels:', xhr);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Gagal memuat data Facebook Pixel.',
                        confirmButtonColor: '#696cff'
                    });
                }
            });
        }

        // Function to clear new pixel form
        function clearNewPixelForm() {
            $('#new_pixel_name').val('');
            $('#new_pixel_id').val('');
            $('#new_pixel_description').val('');
        }

        // Function to save new pixel
        function saveNewPixel() {
            const name = $('#new_pixel_name').val().trim();
            const pixelId = $('#new_pixel_id').val().trim();
            const description = $('#new_pixel_description').val().trim();

            // Validation
            if (!name) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Peringatan!',
                    text: 'Nama pixel harus diisi.',
                    confirmButtonColor: '#696cff'
                });
                $('#new_pixel_name').focus();
                return;
            }

            if (!pixelId) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Peringatan!',
                    text: 'Pixel ID harus diisi.',
                    confirmButtonColor: '#696cff'
                });
                $('#new_pixel_id').focus();
                return;
            }

            // Validate pixel ID format (should be 15 digits)
            if (!/^\d{15}$/.test(pixelId)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Format Tidak Valid!',
                    text: 'Pixel ID harus berupa 15 digit angka.',
                    confirmButtonColor: '#696cff'
                });
                $('#new_pixel_id').focus();
                return;
            }

            // Show loading
            const saveBtn = $('#saveNewPixelBtn');
            const originalText = saveBtn.html();
            saveBtn.prop('disabled', true).html('<i class="bx bx-loader-alt bx-spin me-1"></i>Menyimpan...');

            // Save pixel
            $.ajax({
                url: '{{ route('facebookPixel.store') }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    name: name,
                    pixel_id: pixelId,
                    description: description
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: 'Facebook Pixel berhasil disimpan.',
                            confirmButtonColor: '#696cff'
                        });

                        // Clear form and hide
                        clearNewPixelForm();
                        $('#createPixelForm').slideUp();

                        // Reload pixels and select the new one
                        loadFacebookPixels();
                        setTimeout(() => {
                            $('#facebook_pixel_id').val(response.data.id);
                        }, 500);

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            text: response.message || 'Gagal menyimpan pixel.',
                            confirmButtonColor: '#696cff'
                        });
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Terjadi kesalahan saat menyimpan pixel.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        const errors = Object.values(xhr.responseJSON.errors).flat();
                        errorMessage = errors.join('<br>');
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        html: errorMessage,
                        confirmButtonColor: '#696cff'
                    });
                },
                complete: function() {
                    // Restore button
                    saveBtn.prop('disabled', false).html(originalText);
                }
            });
        }

        // FUNGSI EVENTS DAN PARAMETERS DIHAPUS - TIDAK DIPERLUKAN LAGI
        // Implementasi pixel sekarang menggunakan sistem manual yang sederhana

        function copyToClipboard(event, text) {
            // Coba Clipboard API modern dulu
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopyFeedback(event.currentTarget);
                }).catch(err => {
                    console.error('Clipboard API failed, falling back:', err);
                    fallbackCopy(text, event.currentTarget);
                });
            } else {
                fallbackCopy(text, event.currentTarget);
            }
        }

        function fallbackCopy(text, btn) {
            // Implementasi fallback di sini (seperti contoh sebelumnya)
        }

        function showCopyFeedback(btn) {
            const originalHTML = btn.innerHTML;
            const originalTitle = btn.getAttribute('title');

            btn.innerHTML = '<i class="bx bx-check"></i>';
            btn.setAttribute('title', 'Copied!');

            setTimeout(() => {
                btn.innerHTML = originalHTML;
                btn.setAttribute('title', originalTitle);
            }, 2000);
        }


        // Inisialisasi ClipboardJS
        var clipboard = new ClipboardJS('.clipboard-btn');

        // Event ketika berhasil disalin
        clipboard.on('success', function(e) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: 'Link telah dicopy ke clipboard',
                showConfirmButton: false,
                timer: 1500,
                toast: true,
                timerProgressBar: true,
            });
            e.clearSelection(); // Hapus seleksi setelah copy
        });

        // Event jika gagal
        clipboard.on('error', function(e) {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: 'Tidak bisa menyalin link. Coba lagi.',
                confirmButtonText: 'OK'
            });
        });
    </script>
@endpush
