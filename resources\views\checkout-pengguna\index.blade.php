<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Checkout page for {{ $product->name }}">
    <meta name="keywords" content="checkout, product, {{ $product->name }}">
    <meta name="author" content="Jualinn">
    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow">
    <meta name="googlebot" content="index, follow">
    <meta property="og:type" content="product">
    <meta property="og:title" content="Jualinn - {{ $product->name }}">
    <meta property="og:description" content="{{ $product->description }}">
    <meta property="og:image"
        content="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="Jualinn">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Jualinn - {{ $product->name }}">
    <meta name="twitter:description" content="{{ $product->description }}">
    <meta name="twitter:image"
        content="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg">
    <meta name="twitter:site" content="@jualinn">
    <link rel="canonical" href="{{ url()->current() }}">
    <meta itemprop="name" content="Jualinn - {{ $product->name }}">
    <meta itemprop="description" content="{{ $product->description }}">
    <meta itemprop="image"
        content="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg">
    <script type="application/ld+json">
    {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": "{{ $product->name }}",
        "image": [
            "https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg"
        ],
        "description": "{{ $product->description }}",
        "brand": {
            "@type": "Brand",
            "name": "Jualinn"
        },
        "offers": {
            "@type": "Offer",
            "priceCurrency": "IDR",
            "price": "{{ $product->regular_price }}",
            "availability": "https://schema.org/InStock",
            "url": "{{ url()->current() }}"
        }
    }
    </script>
    <link rel="icon" href="{{ asset('images/favicon.png') }}" type="image/png">
    <title>Jualinn - {{ $product->name }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.21.2/dist/sweetalert2.min.css">
    <!-- Add jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="{{ asset('js/number_format.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.21.2/dist/sweetalert2.all.min.js"></script>
    <style>
        /* Optional: Add some custom styles if needed */
        .container-max-width {
            max-width: 800px;
            /* Adjust max width as needed */
        }

        /* Custom style for active payment button */
        .payment-button.active {
            background-color: #dc2626;
            /* Red 600 */
            color: white;
        }
    </style>
</head>

<body class="bg-gray-100 flex flex-col items-center min-h-screen py-5 px-4 space-y-8"> {{-- Added flex-col and space-y-8 --}}

    <!-- Product Details Card -->
    <div class="bg-white p-6 md:p-8 rounded-lg shadow-xl w-full container-max-width"> {{-- First Card --}}
        <h1 class="text-2xl md:text-3xl font-bold text-center mb-8 text-gray-800">{{ $product->name }}</h1>

        <div class="flex flex-col md:flex-row gap-8 mb-8">
            <!-- Product Image Gallery -->
            <div class="md:w-1/2 flex flex-col items-center">
                @if ($product->images->count() > 0)
                    <img id="mainProductImage" src="{{ asset('storage/' . $product->images[0]->image_path) }}"
                        alt="Gambar produk utama"
                        class="w-full h-auto rounded-lg shadow-md mb-4 object-cover max-h-96" />
                @endif
                <div class="flex gap-3 overflow-x-auto w-full justify-center">
                    @if ($product->images->count() > 0)
                        @foreach ($product->images as $image)
                            <img src="{{ asset('storage/' . $image->image_path) }}" alt="Gambar produk thumbnail"
                                class="w-16 h-16 object-cover rounded-md border-2 border-transparent hover:border-red-500 cursor-pointer transition thumbnail-image"
                                data-image="{{ asset('storage/' . $image->image_path) }}" />
                        @endforeach
                    @endif
                </div>
            </div>

            <!-- Product Info -->
            <div class="md:w-1/2">
                <h2 class="text-xl md:text-2xl font-semibold mb-3 text-gray-800">{{ $product->name }}</h2>
                <p class="text-gray-700 mb-5 leading-relaxed">
                    {{ $product->description }}.
                </p>

                <div class="mb-5">
                    @if ($product->has_variants == false)
                        @php
                            // Buat harga acak antara regular_price + 10% sampai +30%
                            $max = $product->regular_price * 1.3;
                            $min = $product->regular_price * 1.1;
                            $randomPrice = rand($min, $max);
                        @endphp
                        <span class="text-gray-500 line-through mr-3 text-lg">Rp.
                            {{ number_format($randomPrice, 0, ',', '.') }}</span>
                        <span class="text-red-600 font-bold text-3xl">Rp
                            {{ number_format($product->regular_price, 0, ',', '.') }}</span>
                    @else
                        <span class="text-gray-500 line-through mr-3 text-lg">Rp 150.000</span>
                    @endif
                </div>

                <h3 class="font-semibold mb-3 text-gray-800 text-lg">Spesifikasi:</h3>
                <ul class="list-disc list-inside text-gray-700 mb-5 space-y-1">
                    <li>Spesifikasi Penting 1</li>
                    <li>Spesifikasi Utama 2</li>
                    <li>Fitur Unggulan 3</li>
                    <li>Dan seterusnya...</li>
                </ul>

                <!-- Add other details like stock, variations, etc. -->
                {{-- <div class="mb-5 text-gray-700">
                        <span class="font-semibold text-gray-800">Stok:</span> 50 unit
                    </div> --}}
            </div>
        </div>

        <!-- More Product Details/Description -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <h3 class="text-lg md:text-xl font-semibold mb-4 text-gray-800">Detail Produk Lebih Lanjut:</h3>
            <p class="text-gray-700 leading-relaxed">
                {{ $product->advanced_description }}
            </p>
            {{-- <ul class="list-disc list-inside text-gray-700 mt-4 space-y-1">
                    <li>Detail tambahan penting 1</li>
                    <li>Detail tambahan relevan 2</li>
                </ul> --}}
        </div>
    </div>

    <!-- Checkout Card -->
    <form id="form-checkout">
        <div id="checkout-section" class="bg-white p-6 md:p-8 rounded-lg shadow-xl w-full container-max-width">
            {{-- Second Card --}}
            <h2 class="text-xl md:text-2xl font-bold text-center mb-8 text-gray-800">Lengkapi Data Pengiriman &
                Pembayaran</h2>

            <!-- Product Details Summary -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Ringkasan Pesanan</h3>
                <div class="flex items-center mb-4">
                    <img src="https://storage.googleapis.com/a1aa/image/IlpDHil4_kp_i7cuvqdM9aLljN2kgcTlY-WxG9Dz0Cg.jpg"
                        alt="Gambar produk" class="w-16 h-16 rounded-md mr-4 object-cover shadow-sm" width="64"
                        height="64" />
                    <div>
                        <h4 class="font-semibold text-gray-800">{{ $product->name }}</h4>
                        <p class="text-gray-600 text-sm">{{ $product->description }}</p>
                    </div>
                </div>
                <div class="flex justify-between items-center text-gray-700 mb-2">
                    <span>Harga Satuan</span>
                    <span class="font-semibold text-gray-800">Rp
                        {{ number_format($product->regular_price, 0, ',', '.') }}</span>
                </div>
                {{-- Add quantity if needed --}}
                <div class="flex justify-between items-center text-gray-700 mb-2">
                    <span>Jumlah</span>
                    <div class="flex items-center space-x-2">
                        <button type="button" id="decrementQty"
                            class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-lg font-bold">-</button>
                        <input type="number" id="quantity" name="quantity" min="1" value="1"
                            class="w-8 text-center border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-red-500" />
                        <button type="button" id="incrementQty"
                            class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-lg font-bold">+</button>
                    </div>
                </div>
            </div>

            <!-- Payment Method Selection -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Metode Pembayaran</h3>
                <div class="flex border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                    <button type="button"
                        class="payment-button w-1/2 py-3 text-center font-semibold transition-colors duration-200 active"
                        id="codButton">COD (Bayar di Tempat)</button>
                    <button type="button"
                        class="payment-button w-1/2 py-3 text-center font-semibold text-gray-600 bg-white transition-colors duration-200"
                        id="transferButton">Transfer Bank</button>
                </div>
                <!-- Add payment method details/instructions here based on selection -->
                <div id="paymentDetails"
                    class="mt-4 p-4 bg-blue-50 border border-blue-200 text-blue-800 rounded-lg text-sm hidden shadow-inner">
                    <!-- Content will be added by JavaScript -->
                </div>
            </div>

            <!-- Order Summary (Subtotal, Shipping, Fees) -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Rincian Pembayaran</h3>
                <div class="flex justify-between items-center mb-2 text-gray-700">
                    <span>Subtotal Produk</span>
                    <span class="font-semibold text-gray-800" id="subtotalAmount">Rp.
                        {{ number_format($product->regular_price, 0, ',', '.') }}</span>
                </div>
                <div class="flex justify-between items-center mb-2 text-gray-700">
                    <span>Ongkos Kirim</span>
                    <span id="shippingCost" class="text-gray-500 italic">Hitung setelah alamat</span>
                </div>
                <div class="flex justify-between items-center mb-2 text-gray-700" id="codFeeRow">
                    <span>Biaya COD</span>
                    <span id="codFee" class="text-gray-500 italic">Hitung setelah alamat</span>
                </div>
                <div class="border-t border-gray-300 my-4"></div>
                <div class="flex justify-between items-center font-bold text-xl text-gray-800">
                    <span>Total Bayar</span>
                    <span id="totalAmount">Rp. {{ number_format($product->regular_price, 0, ',', '.') }}</span>
                </div>
            </div>

            <!-- Shipping Address Form -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg shadow-sm">
                <h3 class="text-lg font-semibold mb-4 text-gray-800">Alamat Pengiriman</h3>
                <div class="space-y-4">
                    <div>
                        <label for="receiverName"
                            class="block mb-2 text-sm font-semibold text-gray-700">Penerima:</label>
                        <input type="text" id="receiverName"
                            class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                            placeholder="Nama Lengkap" required />
                    </div>
                    <div>
                        <label for="whatsappNumber" class="block mb-2 text-sm font-semibold text-gray-700">Nomor
                            Whatsapp Aktif:</label>
                        <input type="text" id="whatsappNumber"
                            class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                            placeholder="Contoh: 081234567890" required />
                    </div>

                    <label class="block mb-2 text-sm font-semibold text-gray-700">Alamat Lengkap:</label>

                    <div>
                        <div x-data="getAddress()" x-init="init()" class="space-y-4">
                            <!-- Dropdown Provinsi -->
                            <div class="relative">
                                <div @click="open = !open"
                                    class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                    <span x-text="selectedName || 'Pilih Provinsi'" class="text-gray-700"></span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>

                                <div x-show="open" @click.away="open = false"
                                    class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                    <input type="text" x-model="search" @click.stop placeholder="Cari provinsi..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                    <div class="max-h-60 overflow-y-auto">
                                        <template x-for="province in filteredProvinces" :key="province.id">
                                            <div @click="selectProvince(province)"
                                                class="p-2 hover:bg-gray-100 cursor-pointer"
                                                x-text="province.provinsi_name">
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <input type="hidden" name="province" x-model="selected">
                            </div>

                            <!-- Dropdown Kabupaten -->
                            <div class="relative">
                                <div @click="openKabupaten = !openKabupaten"
                                    class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                    <span x-text="selectedNameKabupaten || 'Pilih Kabupaten/Kota'"
                                        class="text-gray-700"></span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>

                                <div x-show="openKabupaten" @click.away="openKabupaten = false"
                                    class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                    <input type="text" x-model="searchKabupaten" @click.stop
                                        placeholder="Cari kabupaten..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                    <div class="max-h-60 overflow-y-auto">
                                        <template x-for="kab in filteredKabupaten" :key="kab.id">
                                            <div @click="selectKabupaten(kab)"
                                                class="p-2 hover:bg-gray-100 cursor-pointer"
                                                x-text="kab.kabupaten_name">
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <input type="hidden" name="kabupaten" x-model="selectedKabupaten">
                            </div>

                            <!-- Dropdown Kecamatan -->
                            <div class="relative">
                                <div @click="openKecamatan = !openKecamatan"
                                    class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                    <span x-text="selectedNameKecamatan || 'Pilih Kecamatan'"
                                        class="text-gray-700"></span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7">
                                        </path>
                                    </svg>
                                </div>

                                <div x-show="openKecamatan" @click.away="openKecamatan = false"
                                    class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                    <input type="text" x-model="searchKecamatan" @click.stop
                                        placeholder="Cari Kecamatan..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                    <div class="max-h-60 overflow-y-auto">
                                        <template x-for="kec in filteredKecamatan" :key="kec.id">
                                            <div @click="selectKecamatan(kec)"
                                                class="p-2 hover:bg-gray-100 cursor-pointer"
                                                x-text="kec.kecamatan_name">
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <input type="hidden" name="kecamatan" x-model="selectedKecamatan">
                            </div>

                            <!-- Dropdown Kelurahan -->
                            <div class="relative">
                                <div @click="openKelurahan = !openKelurahan"
                                    class="w-full p-2 border border-gray-300 rounded-md cursor-pointer flex justify-between items-center bg-white">
                                    <span x-text="selectedNameKelurahan || 'Pilih Kelurahan'"
                                        class="text-gray-700"></span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7">
                                        </path>
                                    </svg>
                                </div>

                                <div x-show="openKelurahan" @click.away="openKelurahan = false"
                                    class="absolute w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                    <input type="text" x-model="searchKelurahan" @click.stop
                                        placeholder="Cari Kelurahan..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none">

                                    <div class="max-h-60 overflow-y-auto">
                                        <template x-for="kel in filteredKelurahan" :key="kel.id">
                                            <div @click="selectKelurahan(kel)"
                                                class="p-2 hover:bg-gray-100 cursor-pointer"
                                                x-text="kel.kelurahan_name">
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <input type="hidden" name="kelurahan" x-model="selectedKelurahan">
                            </div>

                        </div>
                    </div>

                    <div>
                        <textarea
                            class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                            placeholder="Nama Jalan, Nomor Rumah, RT/RW, Patokan (cth: dekat masjid)" rows="3" required></textarea>
                    </div>
                </div>
            </div>

            <!-- Order Button -->
            <button
                class="w-full py-4 bg-red-600 text-white font-bold text-lg rounded-md hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 shadow-lg">Buat
                Pesanan Sekarang</button>
        </div>
    </form>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const codButton = document.getElementById('codButton');
            const transferButton = document.getElementById('transferButton');
            const codFeeRow = document.getElementById('codFeeRow');
            const paymentDetailsDiv = document.getElementById('paymentDetails');
            const paymentButtons = document.querySelectorAll('.payment-button');

            function activateButton(button) {
                paymentButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-red-600', 'text-white');
                    btn.classList.add('bg-white', 'text-gray-600');
                });
                button.classList.add('active', 'bg-red-600', 'text-white');
                button.classList.remove('bg-white', 'text-gray-600');
            }

            // Initial state (COD selected)
            activateButton(codButton);
            codFeeRow.classList.remove('hidden');
            paymentDetailsDiv.classList.add('hidden'); // Hide payment details initially

            codButton.addEventListener('click', function() {
                activateButton(codButton);
                codFeeRow.classList.remove('hidden'); // Show COD fee
                paymentDetailsDiv.classList.add('hidden'); // Hide payment details
                // Update total amount calculation if needed
            });

            transferButton.addEventListener('click', function() {
                activateButton(transferButton);
                codFeeRow.classList.add('hidden'); // Hide COD fee
                paymentDetailsDiv.classList.remove('hidden'); // Show payment details
                paymentDetailsDiv.innerHTML = `
                    <p class="font-semibold mb-2">Instruksi Transfer Bank:</p>
                    <p>Silakan transfer ke rekening:</p>
                    <p>Bank: [Nama Bank]</p>
                    <p>Nomor Rekening: [Nomor Rekening Anda]</p>
                    <p>Atas Nama: [Nama Pemilik Rekening]</p>
                    <p class="mt-3">Jumlah yang harus ditransfer: <span class="font-bold text-lg" id="transferAmount">Rp 100.000</span></p>
                    <p class="text-xs mt-3 text-blue-700">Pesanan akan diproses setelah konfirmasi pembayaran. Mohon transfer sesuai jumlah total.</p>
                `;
                // Update total amount calculation if needed (remove COD fee)
                // This is a simple example, actual calculation should happen here
                document.getElementById('transferAmount').innerText = document.getElementById('totalAmount')
                    .innerText;
            });

        });
        document.addEventListener('DOMContentLoaded', function() {
            const qtyInput = document.getElementById('quantity');
            document.getElementById('decrementQty').onclick = function() {
                let val = parseInt(qtyInput.value) || 1;
                if (val > 1) qtyInput.value = val - 1;
                subtotalAmount.innerText = 'Rp. ' + (val - 1) * {{ $product->regular_price }};
                totalAmount.innerText = 'Rp. ' + (val - 1) * {{ $product->regular_price }};
                // Update total amount calculation if needed (remove COD fee)
                // This is a simple example, actual calculation should happen here
                document.getElementById('subtotalAmount').innerText = 'Rp. ' + number_format((val - 1) *
                    {{ $product->regular_price }});
                document.getElementById('totalAmount').innerText = 'Rp. ' + number_format((val - 1) *
                    {{ $product->regular_price }});
            };
            document.getElementById('incrementQty').onclick = function() {
                let val = parseInt(qtyInput.value) || 1;
                qtyInput.value = val + 1;
                subtotalAmount.innerText = 'Rp. ' + (val + 1) * {{ $product->regular_price }};
                totalAmount.innerText = 'Rp. ' + (val + 1) * {{ $product->regular_price }};
                // Update total amount calculation if needed (remove COD fee)
                // This is a simple example, actual calculation should happen here
                document.getElementById('subtotalAmount').innerText = 'Rp. ' + number_format((val + 1) *
                    {{ $product->regular_price }});
                document.getElementById('totalAmount').innerText = 'Rp. ' + number_format((val + 1) *
                    {{ $product->regular_price }});
            };
        });
        document.addEventListener('DOMContentLoaded', function() {
            const mainImage = document.getElementById('mainProductImage');
            document.querySelectorAll('.thumbnail-image').forEach(function(thumb) {
                thumb.addEventListener('click', function() {
                    if (mainImage) {
                        mainImage.src = this.getAttribute('data-image');
                    }
                });
            });
        });
    </script>
    <script>
        function getAddress() {
            return {
                // State dropdown provinsi
                open: false,
                search: '',
                selected: '',
                selectedName: '',
                provinces: [],

                // State dropdown kabupaten
                openKabupaten: false,
                selectedNameKabupaten: '',
                searchKabupaten: '',
                selectedKabupaten: '',
                kabupatenList: [],

                // State dropdown kecamatan
                openKecamatan: false,
                selectedNameKecamatan: '',
                searchKecamatan: '',
                selectedKecamatan: '',
                kecamatanList: [],

                // State dropdown kelurahan
                openKelurahan: false,
                selectedNameKelurahan: '',
                searchKelurahan: '',
                selectedKelurahan: '',
                kelurahanList: [],

                init() {
                    fetch('{{ route('get-provinsi') }}')
                        .then(res => res.json())
                        .then(data => {
                            this.provinces = data.datas;
                        })
                        .catch(err => console.error('Error fetching provinsi:', err));
                },

                get filteredProvinces() {
                    return this.provinces.filter(p =>
                        p.provinsi_name.toLowerCase().includes(this.search.toLowerCase())
                    );
                },

                get filteredKabupaten() {
                    return this.kabupatenList.filter(k =>
                        k.kabupaten_name.toLowerCase().includes(this.searchKabupaten.toLowerCase())
                    );
                },

                get filteredKecamatan() {
                    return this.kecamatanList.filter(k =>
                        k.kecamatan_name.toLowerCase().includes(this.searchKecamatan.toLowerCase())
                    );
                },

                get filteredKelurahan() {
                    return this.kelurahanList.filter(k =>
                        k.kelurahan_name.toLowerCase().includes(this.searchKelurahan.toLowerCase())
                    );
                },


                selectProvince(province) {
                    this.selected = province.id;
                    this.selectedName = province.provinsi_name;
                    this.open = false;

                    this.selectedKabupaten = '';
                    this.selectedKecamatan = '';
                    this.selectedKelurahan = '';

                    this.kabupatenList = [];
                    this.kecamatanList = [];
                    this.kelurahanList = [];

                    this.fetchKabupaten(province.id);
                },

                selectKabupaten(kabupaten) {
                    this.selectedNameKabupaten = kabupaten.kabupaten_name;
                    this.selectedKabupaten = kabupaten.id;
                    this.openKabupaten = false;

                    this.selectedKecamatan = '';
                    this.selectedKelurahan = '';

                    this.kecamatanList = [];
                    this.kelurahanList = [];

                    this.fetchKecamatan(kabupaten.id);
                },

                selectKecamatan(kecamatan) {
                    this.selectedKecamatan = kecamatan.id;
                    this.selectedNameKecamatan = kecamatan.kecamatan_name;
                    this.openKecamatan = false;

                    this.selectedKelurahan = '';
                    this.kelurahanList = [];

                    this.fetchKelurahan(kecamatan.id); // ✅ Panggil fungsi fetch kelurahan
                },

                selectKelurahan(kelurahan) {
                    this.selectedKelurahan = kelurahan.id;
                    this.selectedNameKelurahan = kelurahan.kelurahan_name;
                    this.openKelurahan = false;
                },

                fetchKabupaten(provinsiId) {
                    const url = `{{ url('get-kabupaten') }}/${provinsiId}`;
                    fetch(url)
                        .then(res => res.json())
                        .then(data => {
                            this.kabupatenList = data.datas;
                        })
                        .catch(err => console.error('Error fetching kabupaten:', err));
                },

                fetchKecamatan(kabupatenId) {
                    const url = `{{ url('get-kecamatan') }}/${kabupatenId}`;
                    fetch(url)
                        .then(res => res.json())
                        .then(data => {
                            this.kecamatanList = data.datas;
                        })
                        .catch(err => console.error('Error fetching kecamatan:', err));
                },

                fetchKelurahan(kecamatanId) {

                    const url = `{{ url('get-kelurahan') }}/${kecamatanId}`;
                    fetch(url)
                        .then(res => res.json())
                        .then(data => {
                            this.kelurahanList = data.results;
                        })
                        .catch(err => console.error('Error fetching kelurahan:', err));
                }


            }
        }

        function checkAddressCompletion() {
            const receiverName = document.getElementById('receiverName').value.trim();
            const whatsappNumber = document.getElementById('whatsappNumber').value.trim();
            const province = document.querySelector('input[name="province"]').value.trim();
            const kabupaten = document.querySelector('input[name="kabupaten"]').value.trim();
            const kecamatan = document.querySelector('input[name="kecamatan"]').value.trim();
            const kelurahan = document.querySelector('input[name="kelurahan"]').value.trim();
            const addressDetails = document.querySelector('textarea').value.trim();

            if (receiverName && whatsappNumber && province && kabupaten && kecamatan && kelurahan && addressDetails) {
                getShippingCost();
            }
        }

        // Add event listeners to form fields
        document.getElementById('receiverName').addEventListener('input', checkAddressCompletion);
        document.getElementById('whatsappNumber').addEventListener('input', checkAddressCompletion);
        document.querySelector('input[name="province"]').addEventListener('change', checkAddressCompletion);
        document.querySelector('input[name="kabupaten"]').addEventListener('change', checkAddressCompletion);
        document.querySelector('input[name="kecamatan"]').addEventListener('change', checkAddressCompletion);
        document.querySelector('input[name="kelurahan"]').addEventListener('change', checkAddressCompletion);
        document.querySelector('textarea').addEventListener('input', checkAddressCompletion);

        function getShippingCost() {
            // Implement your shipping cost calculation logic here
            // For example, you can use an API to get the shipping cost based on the selected address
            const province = document.querySelector('input[name="province"][x-model="selected"]').value;
            const kabupaten = document.querySelector('input[name="kabupaten"][x-model="selectedKabupaten"]').value;
            const kecamatan = document.querySelector('input[name="kecamatan"][x-model="selectedKecamatan"]').value;
            const kelurahan = document.querySelector('input[name="kelurahan"][x-model="selectedKelurahan"]').value;
            const qty = document.getElementById('quantity').value;
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch('{{ route('getCurier') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({
                        nilaiBarang: parseInt({{ $product->regular_price }}) * qty,
                        insurance: 0,
                        origin: 5647,
                        subdistrict_origin: 71066,
                        kecamatan: kecamatan,
                        desa: kelurahan,
                        panjangBarang: 10, // Ganti dengan panjang barang jika ada input
                        lebarBarang: 10, // Ganti dengan lebar barang jika ada input
                        tinggiBarang: 10, // Ganti dengan tinggi barang jika ada input
                        beratBarang: 1000 // Ganti dengan berat barang jika ada input (gram)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Misal response: { shipping_cost: 15000, cod_fee: 5000 }
                    if (data.results[0].cost !== undefined) {
                        document.getElementById('shippingCost').innerText = 'Rp. ' + number_format(data.results[0]
                            .cost);
                    }
                    if (data.results[0].setting.cod_fee_amount !== undefined) {
                        document.getElementById('codFee').innerText = 'Rp. ' + number_format(data.results[0].setting
                            .cod_fee_amount);
                    }

                    // Update total bayar sesuai metode pembayaran
                    let subtotal = parseInt({{ $product->regular_price }}) * qty;
                    let shipping = parseInt(data.results[0].cost) || 0;
                    let codFee = parseInt(data.results[0].setting.cod_fee_amount) || 0;

                    // Cek metode pembayaran aktif
                    // Listen for payment method changes to recalculate shipping cost
                    let isCOD = document.getElementById('codButton').classList.contains('active');
                    document.getElementById('codButton').onclick = function() {
                        setTimeout(getShippingCost, 0.1);
                    };
                    document.getElementById('transferButton').onclick = function() {
                        setTimeout(getShippingCost, 0.1);
                    };


                    let total = subtotal + shipping + (isCOD ? codFee : 0);

                    document.getElementById('totalAmount').innerText = 'Rp. ' + number_format(total);

                    // Jika transfer bank, update juga jumlah transfer di instruksi transfer
                    if (!isCOD && document.getElementById('transferAmount')) {
                        document.getElementById('transferAmount').innerText = 'Rp. ' + number_format(total);
                    }

                })
                .catch(err => {
                    document.getElementById('shippingCost').innerText = 'Gagal menghitung ongkir';
                    document.getElementById('codFee').innerText = '-';
                });
        }

        document.getElementById('form-checkout').addEventListener('submit', function(e) {
            e.preventDefault();

            // Ambil data form
            const receiverName = document.getElementById('receiverName').value.trim();
            const whatsappNumber = document.getElementById('whatsappNumber').value.trim();
            const province = document.querySelector('input[name="province"]').value.trim();
            const kabupaten = document.querySelector('input[name="kabupaten"]').value.trim();
            const kecamatan = document.querySelector('input[name="kecamatan"]').value.trim();
            const kelurahan = document.querySelector('input[name="kelurahan"]').value.trim();
            const addressDetails = document.querySelector('textarea').value.trim();
            const qty = parseInt(document.getElementById('quantity').value) || 1;
            const paymentMethod = document.getElementById('codButton').classList.contains('active') ? 'cod' :
                'bank_transfer';
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Validasi sederhana
            if (!receiverName || !whatsappNumber || !province || !kabupaten || !kecamatan || !kelurahan || !
                addressDetails) {
                alert('Mohon lengkapi semua data pengiriman.');
                return;
            }

            // Data yang akan dikirim
            const data = {
                product: "{{ $product->name }}",
                product_code: "{{ $product->product_code ?? '' }}",
                product_price: {{ $product->regular_price }},
                quantity: qty,
                name: receiverName,
                phone: whatsappNumber,
                address: addressDetails,
                province: province,
                city: kabupaten,
                subdistrict: kecamatan,
                zip: kelurahan,
                payment_method: paymentMethod,
                courier: 'NINJA - STANDARD', // Isi sesuai pilihan kurir jika ada, atau kosong
                shipping_cost: 0, // Isi sesuai hasil perhitungan ongkir
                gross_revenue: 0, // Isi sesuai perhitungan total sebelum potongan
                net_revenue: 0, // Isi sesuai perhitungan total setelah potongan
                weight: 1000 // Isi sesuai berat produk (gram)
            };

            // Disable button agar tidak double submit
            const btn = this.querySelector('button[type="submit"],button:not([type])');
            if (btn) {
                btn.disabled = true;
                btn.innerText = 'Memproses...';
            }

            fetch('{{ route('checkout-order') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify(data)
                })
                .then(res => res.json())
                .then(res => {
                    if (res.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: res.message || 'Pesanan berhasil dibuat!',
                            confirmButtonColor: '#dc2626'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: res.message || 'Gagal membuat pesanan.',
                            confirmButtonColor: '#dc2626'
                        });
                    }
                })
                .catch(() => {
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: 'Terjadi kesalahan saat membuat pesanan.',
                        confirmButtonColor: '#dc2626'
                    });
                })
                .finally(() => {
                    if (btn) {
                        btn.disabled = false;
                        btn.innerText = 'Buat Pesanan Sekarang';
                    }
                });
        });
    </script>

</body>

</html>
