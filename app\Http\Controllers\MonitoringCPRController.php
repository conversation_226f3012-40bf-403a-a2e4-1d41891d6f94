<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMonitoringCprRequest;
use App\Services\MonitoringCpr\DataMonitoringCprService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Services\Facebook\FacebookAdService;
use App\Helpers\TimeoutHelper;

class MonitoringCprController extends Controller
{
    public function __construct(
        protected DataMonitoringCprService $dataMonitoringCprService,
        protected FacebookAdService $facebookAdService
    ) {
        $this->facebookAdService = $facebookAdService;
    }

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        $idAkunString = auth()->user()->fbads_id;
        $namaAkunString = auth()->user()->fbads_name;

        $idAkunArray = array_map('trim', explode(',', $idAkunString));
        $namaAkunArray = array_map('trim', explode(',', $namaAkunString));
        $akun_ads = [];

        foreach ($idAkunArray as $key => $id) {
            $akun_ads[] = [
                'id' => $id,
                'nama' => $namaAkunArray[$key] ?? null
            ];
        }

        return view('monitoring-cpr.index', [
            'title' => 'Monitoring CPR',
            'akun' => $akun_ads
        ]);
    }


    public function data()
    {
        $dateRange = request()->get('date', date('Y-m-01') . ' to ' . date('Y-m-t'));
        $idAkun = request()->get('idAkun');
        $status = request()->get('status');

        $data = $this->dataMonitoringCprService->getDataIdUnique($dateRange, $idAkun, $status);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('cpr', function ($data) {
                $hasil = floatval($data->hasil);
                $belanja = floatval($data->biaya_dibelanjakan);

                if ($hasil != 0) {
                    return number_format($belanja / $hasil, 2);
                }

                return '0';
            })
            ->editColumn('status', function ($data) {
                $badgeClass = 'secondary';
                switch ($data->status) {
                    case 'ACTIVE':
                        $badgeClass = 'success';
                        break;
                    case 'PAUSED':
                        $badgeClass = 'warning';
                        break;
                    case 'ARCHIVED':
                        $badgeClass = 'danger';
                        break;
                }
                return '<span class="badge bg-' . $badgeClass . '">' . $data->status . '</span>';
            })
            ->editColumn('biaya_dibelanjakan', function ($data) {
                $value = floatval($data->biaya_dibelanjakan);
                if ($value == 0)
                    return '-';

                $rupiah = $value * 278.62;
                return 'Rp ' . number_format($rupiah, 2, ',', '.');
            })
            ->editColumn('hasil', function ($data) {
                $value = intval($data->hasil);
                return $value > 0 ? number_format($value, 0, ',', '.') : '0';
            })
            ->editColumn('nama_kampanye', function ($data) {
                $name = $data->nama_kampanye ?? '-';
                return '<div style="text-align:left;" title="' . htmlspecialchars($name) . '">' .
                    (strlen($name) > 30 ? substr($name, 0, 30) . '...' : $name) . '</div>';
            })
            ->rawColumns(['cpr', 'status', 'nama_kampanye'])
            ->make(true);
    }

    /**
     * Get statistics for dashboard
     */
    public function statistics()
    {
        $dateRange = request()->get('date', date('Y-m-01') . ' to ' . date('Y-m-t'));
        $idAkun = request()->get('idAkun');
        $status = request()->get('status');

        $stats = $this->dataMonitoringCprService->getStatistics($dateRange, $idAkun, $status);

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get real-time sync status
     */
    public function syncStatus()
    {
        $status = \Cache::get('cpr_auto_sync_status', [
            'status' => 'unknown',
            'message' => 'No sync data available',
            'last_sync' => null,
            'next_sync' => null
        ]);

        return response()->json([
            'success' => true,
            'data' => $status
        ]);
    }

    /**
     * Trigger manual sync for specific account
     */
    public function triggerSync(Request $request)
    {
        $accountId = $request->get('account_id');

        if (!$accountId) {
            return response()->json([
                'success' => false,
                'message' => 'Account ID is required'
            ], 400);
        }

        try {
            // Dispatch auto-sync job for specific account
            \App\Jobs\AutoSyncCprDataJob::dispatch(true, [$accountId]);

            return response()->json([
                'success' => true,
                'message' => 'Sync job dispatched successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to dispatch sync job: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Trigger manual sync for specific account
     */
    public function sync(Request $request)
    {
        $idAkun = $request->get('idAkun');

        if (!$idAkun) {
            return response()->json([
                'success' => false,
                'message' => 'Account ID is required'
            ], 400);
        }

        try {
            // Dispatch sync job
            \App\Jobs\SyncCprDataJob::dispatch($idAkun, auth()->id());

            return response()->json([
                'success' => true,
                'message' => 'Sync job dispatched successfully. Data will be updated shortly.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to dispatch sync job: ' . $e->getMessage()
            ], 500);
        }
    }

    public function create(Request $request)
    {
        $idAkun = 'act_' . $request->idAkun;
        $namaAkun = $request->namaAkun;

        $campaign = $this->facebookAdService->getDataCampaigns($idAkun); // CAMPAIGN

        if ($campaign) {
            $campaign_sub = $this->facebookAdService->getDataAdSet($idAkun); // SET IKLAN
            $insightsResponse = $this->facebookAdService->getDataInsights($idAkun); // INSIGHTS
        }

        // Index campaign by id
        $indexedCampaign = [];
        foreach ($campaign as $item) {
            $indexedCampaign[$item['id_kampanye']] = $item;
        }

        // Total jangkauan per kampanye
        $indexedInsights = [];
        foreach ($insightsResponse as $insight) {
            $id_kampanye = $insight['id_kampanye'] ?? null;

            $jangkauan = isset($insight['jangkauan']) ? (int) $insight['jangkauan'] : 0;
            $impresi = isset($insight['impresi']) ? (int) $insight['impresi'] : 0;
            $biaya_dibelanjakan = isset($insight['biaya_dibelanjakan']) ? (int) $insight['biaya_dibelanjakan'] : 0;

            // Ambil actions
            $actions = $insight['aksi'] ?? [];

            // Cari value dari action_type tertentu, misal 'web_in_store_purchase'
            $webInStorePurchase = 0;
            foreach ($actions as $action) {
                if (($action['action_type'] ?? '') === 'web_in_store_purchase') {
                    $webInStorePurchase = (int) $action['value'];
                    break; // keluar setelah ketemu
                }
            }

            if ($id_kampanye) {
                if (!isset($indexedInsights[$id_kampanye])) {
                    $indexedInsights[$id_kampanye] = [
                        'jangkauan' => 0,
                        'impresi' => 0, // tambahkan ini agar tidak undefined
                        'biaya_dibelanjakan' => 0, // tambahkan ini agar tidak undefined
                        'total_web_in_store_purchase' => 0, // inisialisasi field baru
                    ];
                }

                $indexedInsights[$id_kampanye]['jangkauan'] += $jangkauan;
                $indexedInsights[$id_kampanye]['impresi'] += $impresi;
                $indexedInsights[$id_kampanye]['biaya_dibelanjakan'] += $biaya_dibelanjakan;
                $indexedInsights[$id_kampanye]['total_web_in_store_purchase'] += $webInStorePurchase;
            }
        }
        // return $campaign_sub;
        foreach ($campaign_sub as &$adSet) {
            $id_kampanye = $adSet['id_kampanye'];

            if (isset($indexedCampaign[$id_kampanye])) {
                $adSet['nama_kampanye'] = $indexedCampaign[$id_kampanye]['nama_kampanye'] ?? '-';
                $adSet['adlabels'] = $indexedCampaign[$id_kampanye]['adlabels'] ?? '-';
                $adSet['status'] = $indexedCampaign[$id_kampanye]['status'] ?? '-';
                $adSet['strategi_bid'] = $indexedCampaign[$id_kampanye]['strategi_bid'] ?? '-';
                $adSet['anggaran_harian'] = $indexedCampaign[$id_kampanye]['anggaran_harian'] ?? '0';
                $adSet['waktu_dibuat'] = $indexedCampaign[$id_kampanye]['waktu_dibuat'] ?? '-';
                $adSet['waktu_selesai'] = $indexedCampaign[$id_kampanye]['waktu_selesai'] ?? '-';

                // Total insights kampanye
                $adSet['jangkauan'] = (int) ($indexedInsights[$id_kampanye]['jangkauan'] ?? 0);
                $adSet['impresi'] = (int) ($indexedInsights[$id_kampanye]['impresi'] ?? 0);
                $adSet['jumlah_dibelanjakan'] = (int) ($indexedInsights[$id_kampanye]['biaya_dibelanjakan'] ?? 0);
                $adSet['hasil'] = (int) ($indexedInsights[$id_kampanye]['total_web_in_store_purchase'] ?? 0);

                // Proses atribusi
                $atribusiList = $adSet['spek_atribusi'] ?? [];
                $atribusiString = [];

                foreach ($atribusiList as $atribusi) {
                    $days = $atribusi['window_days'] ?? 0;
                    $label = match ($atribusi['event_type']) {
                        'CLICK_THROUGH' => ' klik',
                        'VIEW_THROUGH' => ' tayangan',
                        'ENGAGED_VIDEO_VIEW' => ' interaksi video',
                        default => ' lainnya',
                    };
                    $atribusiString[] = $days . $label;
                }

                $adSet['spek_atribusi'] = implode(' ', $atribusiString);
                $adSet['cpr'] = ($adSet['hasil'] ?? 0) != 0 ? ($adSet['jumlah_dibelanjakan'] / $adSet['hasil']) : 0;
            }

            // return $adSet;
            // SAVE 'DATA'MONITORING CPR
            $data = [
                'nama_set_iklan' => $adSet['nama_set_iklan'] ?? null,
                'id_akun' => $adSet['id_akun'] ?? 0,
                'id_kampanye' => $adSet['id_kampanye'] ?? 0,
                'anggaran_harian' => isset($adSet['anggaran_harian']) ? number_format($adSet['anggaran_harian'] / 100, 2, '.', '') : 0.00,
                'spek_atribusi' => $adSet['spek_atribusi'] ?? null,
                'status' => $adSet['status'] ?? null,
                'tanggal_mulai' => isset($adSet['waktu_dibuat']) ? date('Y-m-d', strtotime($adSet['waktu_dibuat'])) : null,
                'tanggal_berhenti' => isset($adSet['tanggal_berhenti']) ? date('Y-m-d', strtotime($adSet['tanggal_berhenti'])) : null,
                'nama_kampanye' => $adSet['nama_kampanye'] ?? null,
                'adlabels' => $adSet['adlabels'] ?? null,
                'strategi_penawaran' => $adSet['strategi_bid'] ?? null,
                'jangkauan' => $adSet['jangkauan'] ?? 0,
                'impresi' => $adSet['impresi'] ?? 0,
                'biaya_dibelanjakan' => $adSet['jumlah_dibelanjakan'] ?? 0,
                'biaya_per_klik' => $adSet['biaya_per_klik'] ?? 0,
                'biaya_perhasil' => $adSet['biaya_perhasil'] ?? 0,
                'hasil' => $adSet['hasil'] ?? 0,
            ];

            // return $data;
            $this->dataMonitoringCprService->updateOrCreate($data);
        }
        unset($insight); // Break reference
        return view('monitoring-cpr.modal', [
            'title' => 'Kampanye (Iklan)',
            'levelUser' => LevelUser::toArray(),
            'isEditing' => false,
            'namaAkun' => $namaAkun,
            'idAkun' => $idAkun,
            'campaign' => $campaign_sub
        ]);
    }
}
