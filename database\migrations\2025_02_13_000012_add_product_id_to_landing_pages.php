<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('landing_pages', function (Blueprint $table) {
            // Add product relationship only if it doesn't exist
            if (!Schema::hasColumn('landing_pages', 'product_id')) {
                $table->foreignId('product_id')->nullable()->after('user_id')->constrained()->onDelete('set null');
            }

            // Remove manual product name field since we'll get it from product relationship
            if (Schema::hasColumn('landing_pages', 'product_name')) {
                $table->dropColumn('product_name');
            }
        });

        // Also add product relationship to section 2-5 if needed
        Schema::table('landing_pages', function (Blueprint $table) {
            if (!Schema::hasColumn('landing_pages', 'section2_product_id')) {
                $table->foreignId('section2_product_id')->nullable()->after('section2_content')->constrained('products')->onDelete('set null');
            }
            if (!Schema::hasColumn('landing_pages', 'section3_product_id')) {
                $table->foreignId('section3_product_id')->nullable()->after('section3_content')->constrained('products')->onDelete('set null');
            }
            if (!Schema::hasColumn('landing_pages', 'section4_product_id')) {
                $table->foreignId('section4_product_id')->nullable()->after('section4_content')->constrained('products')->onDelete('set null');
            }
            if (!Schema::hasColumn('landing_pages', 'section5_product_id')) {
                $table->foreignId('section5_product_id')->nullable()->after('section5_content')->constrained('products')->onDelete('set null');
            }

            // Remove manual product name fields if they exist
            $columnsToRemove = [];
            if (Schema::hasColumn('landing_pages', 'section2_product_name')) {
                $columnsToRemove[] = 'section2_product_name';
            }
            if (Schema::hasColumn('landing_pages', 'section3_product_name')) {
                $columnsToRemove[] = 'section3_product_name';
            }
            if (Schema::hasColumn('landing_pages', 'section4_product_name')) {
                $columnsToRemove[] = 'section4_product_name';
            }
            if (Schema::hasColumn('landing_pages', 'section5_product_name')) {
                $columnsToRemove[] = 'section5_product_name';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });

        // Add product relationship to section 6
        Schema::table('landing_page_section6', function (Blueprint $table) {
            if (!Schema::hasColumn('landing_page_section6', 'product_id')) {
                $table->foreignId('product_id')->nullable()->after('landing_page_id')->constrained()->onDelete('set null');
            }

            // Remove manual product name field
            if (Schema::hasColumn('landing_page_section6', 'product_name')) {
                $table->dropColumn('product_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('landing_page_section6', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
            $table->string('product_name')->nullable();
        });

        Schema::table('landing_pages', function (Blueprint $table) {
            $table->dropForeign(['section2_product_id']);
            $table->dropForeign(['section3_product_id']);
            $table->dropForeign(['section4_product_id']);
            $table->dropForeign(['section5_product_id']);

            $table->dropColumn([
                'section2_product_id',
                'section3_product_id',
                'section4_product_id',
                'section5_product_id'
            ]);

            $table->string('section2_product_name')->nullable();
            $table->string('section3_product_name')->nullable();
            $table->string('section4_product_name')->nullable();
            $table->string('section5_product_name')->nullable();
        });

        Schema::table('landing_pages', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
            $table->string('product_name')->nullable();
        });
    }
};
