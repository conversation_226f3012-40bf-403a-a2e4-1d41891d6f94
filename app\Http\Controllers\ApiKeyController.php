<?php

namespace App\Http\Controllers;

use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiKeyController extends Controller
{
    /**
     * Display the API key configuration page.
     */
    public function index()
    {
        $apiKey = ApiKey::getConfig();

        return view('api-keys.index', [
            'title' => 'API Key Configuration',
            'apiKey' => $apiKey
        ]);
    }

    /**
     * Update the API key configuration.
     */
    public function update(Request $request)
    {
        // Validasi input
        $validator = Validator::make($request->all(), [
            'rajaongkir_endpoint' => 'nullable|url',
            'rajaongkir_api_key' => 'nullable|string',
            'kiriminaja_env' => 'required|in:dev,prod',
            'kiriminaja_dev_base_url' => 'nullable|url',
            'kiriminaja_dev_api_key' => 'nullable|string',
            'kiriminaja_prod_base_url' => 'nullable|url',
            'kiriminaja_prod_api_key' => 'nullable|string',
            'facebook_app_id' => 'nullable|string',
            'facebook_app_secret' => 'nullable|string',
            'facebook_access_token' => 'nullable|string',
            'woowa_phone_num' => 'nullable|string',
            'woowa_base_url' => 'nullable|url',
            'woowa_api_key' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation Error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Update atau create konfigurasi API Key
            ApiKey::updateConfig($request->all());

            return response()->json([
                'success' => true,
                'message' => 'API Key configuration updated successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating API Key configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request)
    {
        $service = $request->input('service');

        \Log::info("Testing connection for service: {$service}");

        try {
            switch ($service) {
                case 'rajaongkir':
                    $serviceInstance = app(\App\Services\RajaOngkir\RajaOngkirService::class);

                    // First check if configured
                    if (!$serviceInstance->isConfigured()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'RajaOngkir is not configured. Please set the API key first.'
                        ]);
                    }

                    // Try full test first
                    $result = $serviceInstance->testConnection();

                    // If that fails, try basic test
                    if (!$result['success']) {
                        \Log::info('Full test failed, trying basic test for RajaOngkir');
                        $basicResult = $serviceInstance->basicConnectionTest();
                        if ($basicResult['success']) {
                            $result['message'] .= ' (Basic connectivity: OK)';
                        }
                    }

                    break;

                case 'kiriminaja':
                    $serviceInstance = app(\App\Services\KiriminAja\KiriminAjaBaseService::class);

                    // First check if configured
                    if (!$serviceInstance->isConfigured()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'KiriminAja is not configured. Please set the environment and API key first.'
                        ]);
                    }

                    $result = $serviceInstance->testConnection();
                    break;

                case 'facebook':
                    $serviceInstance = app(\App\Services\Facebook\FacebookAdService::class);

                    // First check if configured
                    if (!$serviceInstance->isConfigured()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Facebook is not configured. Please set App ID, App Secret, and Access Token first.'
                        ]);
                    }

                    $result = $serviceInstance->testConnection();
                    break;

                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Service not supported: ' . $service
                    ], 400);
            }

            \Log::info("Test connection result for {$service}", $result);

            return response()->json($result);

        } catch (\Exception $e) {
            \Log::error("API connection test error for {$service}: " . $e->getMessage(), [
                'service' => $service,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'debug_info' => [
                    'service' => $service,
                    'error_type' => get_class($e)
                ]
            ], 500);
        }
    }

    /**
     * Refresh configuration cache for all services
     */
    public function refreshConfig()
    {
        try {
            // Clear all API configuration caches
            \Cache::forget('rajaongkir_config');
            \Cache::forget('kiriminaja_config');
            \Cache::forget('facebook_config');

            // Refresh configurations in services
            $rajaOngkir = app(\App\Services\RajaOngkir\RajaOngkirService::class);
            $rajaOngkir->refreshConfig();

            $kiriminAja = app(\App\Services\KiriminAja\KiriminAjaBaseService::class);
            $kiriminAja->refreshConfig();

            $facebook = app(\App\Services\Facebook\FacebookAdService::class);
            $facebook->refreshConfig();

            return response()->json([
                'success' => true,
                'message' => 'Configuration cache refreshed successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Configuration refresh error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Debug configuration status
     */
    public function debugConfig()
    {
        try {
            $apiKey = ApiKey::first();

            if (!$apiKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'No API key configuration found in database'
                ]);
            }

            $config_status = [
                'rajaongkir' => [
                    'endpoint' => !empty($apiKey->rajaongkir_endpoint),
                    'api_key' => !empty($apiKey->rajaongkir_api_key),
                    'endpoint_value' => $apiKey->rajaongkir_endpoint,
                    'api_key_length' => strlen($apiKey->rajaongkir_api_key ?? '')
                ],
                'kiriminaja' => [
                    'env' => !empty($apiKey->kiriminaja_env),
                    'dev_base_url' => !empty($apiKey->kiriminaja_dev_base_url),
                    'dev_api_key' => !empty($apiKey->kiriminaja_dev_api_key),
                    'prod_base_url' => !empty($apiKey->kiriminaja_prod_base_url),
                    'prod_api_key' => !empty($apiKey->kiriminaja_prod_api_key),
                    'env_value' => $apiKey->kiriminaja_env,
                    'dev_base_url_value' => $apiKey->kiriminaja_dev_base_url,
                    'prod_base_url_value' => $apiKey->kiriminaja_prod_base_url
                ],
                'facebook' => [
                    'app_id' => !empty($apiKey->facebook_app_id),
                    'app_secret' => !empty($apiKey->facebook_app_secret),
                    'access_token' => !empty($apiKey->facebook_access_token),
                    'app_id_length' => strlen($apiKey->facebook_app_id ?? ''),
                    'app_secret_length' => strlen($apiKey->facebook_app_secret ?? ''),
                    'access_token_length' => strlen($apiKey->facebook_access_token ?? '')
                ],
                'woowa' => [
                    'phone_num' => !empty($apiKey->woowa_phone_num),
                    'base_url' => !empty($apiKey->woowa_base_url),
                    'api_key' => !empty($apiKey->woowa_api_key),
                    'base_url_value' => $apiKey->woowa_base_url
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'Configuration status retrieved',
                'data' => $config_status
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test basic connectivity for all services
     */
    public function testBasicConnectivity()
    {
        $results = [];

        // Test RajaOngkir connectivity
        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->head('https://pro.rajaongkir.com', ['timeout' => 5]);
            $results['rajaongkir'] = [
                'success' => $response->getStatusCode() < 500,
                'status_code' => $response->getStatusCode(),
                'message' => 'Basic connectivity: ' . ($response->getStatusCode() < 500 ? 'OK' : 'Server Error')
            ];
        } catch (\Exception $e) {
            $results['rajaongkir'] = [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }

        // Test KiriminAja connectivity
        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->head('https://tdev.kiriminaja.com', ['timeout' => 5]);
            $results['kiriminaja_dev'] = [
                'success' => $response->getStatusCode() < 500,
                'status_code' => $response->getStatusCode(),
                'message' => 'Dev basic connectivity: ' . ($response->getStatusCode() < 500 ? 'OK' : 'Server Error')
            ];

            $response = $client->head('https://api.kiriminaja.com', ['timeout' => 5]);
            $results['kiriminaja_prod'] = [
                'success' => $response->getStatusCode() < 500,
                'status_code' => $response->getStatusCode(),
                'message' => 'Prod basic connectivity: ' . ($response->getStatusCode() < 500 ? 'OK' : 'Server Error')
            ];
        } catch (\Exception $e) {
            $results['kiriminaja'] = [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }

        // Test Facebook connectivity
        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->head('https://graph.facebook.com', ['timeout' => 5]);
            $results['facebook'] = [
                'success' => $response->getStatusCode() < 500,
                'status_code' => $response->getStatusCode(),
                'message' => 'Basic connectivity: ' . ($response->getStatusCode() < 500 ? 'OK' : 'Server Error')
            ];
        } catch (\Exception $e) {
            $results['facebook'] = [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Basic connectivity test completed',
            'data' => $results
        ]);
    }
}
