<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class LandingPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'user_id',
        'product_id',
        'facebook_pixel_id',
        'pixel_events',
        'pixel_event_parameters',
        'is_active',
        'visitors',

        // Section 1
        'background_color',
        'main_title',
        'sub_title',
        'section_image',
        'content_description',

        // Section 2
        'section2_product_id',
        'section2_background_color',
        'section2_title',
        'section2_sub_title',
        'section2_image',
        'section2_content',

        // Section 3
        'section3_enabled',
        'section3_product_id',
        'section3_background_color',
        'section3_title',
        'section3_sub_title',
        'section3_image',
        'section3_content',

        // Section 4
        'section4_enabled',
        'section4_product_id',
        'section4_background_color',
        'section4_title',
        'section4_sub_title',
        'section4_image',
        'section4_content',

        // Section 5
        'section5_enabled',
        'section5_product_id',
        'section5_background_color',
        'section5_title',
        'section5_sub_title',
        'section5_image',
        'section5_content',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'visitors' => 'integer',
        'pixel_events' => 'array',
        'pixel_event_parameters' => 'array',
        'section3_enabled' => 'boolean',
        'section4_enabled' => 'boolean',
        'section5_enabled' => 'boolean'
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($landingPage) {
            if (empty($landingPage->slug)) {
                $landingPage->slug = Str::slug($landingPage->name);
            }
        });

        static::updating(function ($landingPage) {
            if ($landingPage->isDirty('name')) {
                $landingPage->slug = Str::slug($landingPage->name);
            }
        });
    }

    /**
     * Get the user that owns the landing page.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the main product for this landing page.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the section 2 product.
     */
    public function section2Product()
    {
        return $this->belongsTo(Product::class, 'section2_product_id');
    }

    /**
     * Get the section 3 product.
     */
    public function section3Product()
    {
        return $this->belongsTo(Product::class, 'section3_product_id');
    }

    /**
     * Get the section 4 product.
     */
    public function section4Product()
    {
        return $this->belongsTo(Product::class, 'section4_product_id');
    }

    /**
     * Get the section 5 product.
     */
    public function section5Product()
    {
        return $this->belongsTo(Product::class, 'section5_product_id');
    }

    /**
     * Get the Facebook pixel for this landing page.
     */
    public function facebookPixel()
    {
        return $this->belongsTo(FacebookPixel::class);
    }

    /**
     * Get the section 6 configuration for this landing page.
     */
    public function section6()
    {
        return $this->hasOne(LandingPageSection6::class);
    }

    /**
     * Get the URL for this landing page.
     */
    public function getUrlAttribute()
    {
        return route('landing.show', $this->slug);
    }

    /**
     * Increment visitor count.
     */
    public function incrementVisitors()
    {
        $this->increment('visitors');
    }

    /**
     * Scope for active landing pages.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for landing pages by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function setFacebookPixelIdAttribute($value)
    {
        $this->attributes['facebook_pixel_id'] = empty($value) ? null : $value;
    }
}
