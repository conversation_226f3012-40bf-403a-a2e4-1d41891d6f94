<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\AutoSyncCprDataJob;
use Illuminate\Support\Facades\Cache;

class AutoSyncCprDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cpr:auto-sync 
                            {--force : Force sync even if recently synced}
                            {--accounts= : Comma-separated list of account IDs to sync}
                            {--queue : Run in background queue}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically sync CPR data from Facebook Ads API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        $accounts = $this->option('accounts');
        $useQueue = $this->option('queue');

        // Parse account IDs if provided
        $accountIds = null;
        if ($accounts) {
            $accountIds = array_map('trim', explode(',', $accounts));
            $this->info('Syncing specific accounts: ' . implode(', ', $accountIds));
        }

        // Check if we should skip sync (unless forced)
        if (!$force && $this->shouldSkipSync()) {
            $this->warn('Skipping sync - recently synced. Use --force to override.');
            return 0;
        }

        $this->info('Starting CPR auto-sync...');

        try {
            if ($useQueue) {
                // Dispatch to queue
                AutoSyncCprDataJob::dispatch($force, $accountIds);
                $this->info('Auto-sync job dispatched to queue successfully.');
            } else {
                // Run synchronously
                $job = new AutoSyncCprDataJob($force, $accountIds);
                $job->handle(
                    app(\App\Services\Facebook\FacebookAdService::class),
                    app(\App\Services\MonitoringCpr\DataMonitoringCprService::class)
                );
                $this->info('CPR data auto-sync completed successfully.');
            }

            // Show sync status
            $this->showSyncStatus();

            return 0;

        } catch (\Exception $e) {
            $this->error('Auto-sync failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Check if we should skip sync based on recent activity
     */
    private function shouldSkipSync(): bool
    {
        $lastSync = Cache::get('cpr_last_auto_sync');
        
        if (!$lastSync) {
            return false;
        }

        // Don't sync more than once every 5 minutes
        $timeSinceLastSync = now()->diffInMinutes($lastSync);
        return $timeSinceLastSync < 5;
    }

    /**
     * Show current sync status
     */
    private function showSyncStatus(): void
    {
        $status = Cache::get('cpr_auto_sync_status');
        
        if (!$status) {
            $this->warn('No sync status available.');
            return;
        }

        $this->newLine();
        $this->info('=== Sync Status ===');
        $this->line('Status: ' . ucfirst($status['status']));
        $this->line('Message: ' . $status['message']);
        $this->line('Last Sync: ' . $status['last_sync']->format('Y-m-d H:i:s'));
        $this->line('Next Sync: ' . $status['next_sync']->format('Y-m-d H:i:s'));
        
        if (isset($status['total_synced'])) {
            $this->line('Total Synced: ' . $status['total_synced']);
        }
        
        if (isset($status['total_errors']) && $status['total_errors'] > 0) {
            $this->warn('Total Errors: ' . $status['total_errors']);
        }

        // Show detailed results if available
        if (isset($status['results']) && !empty($status['results'])) {
            $this->newLine();
            $this->info('=== Account Results ===');
            
            $headers = ['Account ID', 'Account Name', 'Status', 'Campaigns', 'Error'];
            $rows = [];
            
            foreach ($status['results'] as $result) {
                $rows[] = [
                    $result['account_id'],
                    $result['account_name'] ?? 'N/A',
                    $result['success'] ? 'Success' : 'Failed',
                    $result['campaigns_synced'] ?? 0,
                    $result['error'] ?? '-'
                ];
            }
            
            $this->table($headers, $rows);
        }
    }
}
