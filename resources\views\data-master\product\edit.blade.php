@extends('templates.app')

@section('title', 'Edit Produk')

@push('style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    <link href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet" />
    <link href="{{ asset('css/product/product-images-edit.css') }}" rel="stylesheet" />
    <style>
        /* Modern Dropzone Styling */
        .dropzone-modern {
            border: 2px dashed #696cff;
            border-radius: 0.75rem;
            background: #f8f9fa;
            min-height: 200px;
            padding: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dropzone-modern:hover {
            background: #f0f1f5;
            border-color: #5d60e6;
        }

        .dropzone-modern .dz-message {
            margin: 2em 0;
            text-align: center;
        }

        .dropzone .dz-preview {
            margin: 1rem;
        }

        /* Image Preview Container */
        .image-preview-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-preview {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
            cursor: grab;
        }

        .image-preview:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .image-preview:hover img {
            transform: scale(1.05);
        }

        .image-preview .controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: space-between;
            padding: 5px 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-preview:hover .controls {
            opacity: 1;
        }

        .image-preview .controls button {
            background: transparent;
            border: none;
            color: white;
            cursor: pointer;
            padding: 5px;
            font-size: 1rem;
        }

        .image-preview .controls button:hover {
            color: #ffc107;
        }

        .image-preview .controls button.delete-btn:hover {
            color: #dc3545;
        }

        .image-preview .primary-badge {
            position: absolute;
            top: 30px;
            left: 5px;
            background: rgba(255, 193, 7, 0.95);
            color: #212529;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            z-index: 3;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 193, 7, 1);
            box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
        }

        .image-preview .temp-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #6c757d;
            color: #fff;
            font-size: 0.7rem;
            padding: 3px 8px;
            border-radius: 20px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .image-preview.temp-preview {
            border: 2px dashed #6c757d;
        }

        /* Sortable styling */
        .image-preview.ui-sortable-helper {
            cursor: grabbing;
        }

        .image-preview-placeholder {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
            aspect-ratio: 1;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--bs-primary) !important;
            color: #fff !important;
            border: none !important;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.25rem;
            margin: 0.25rem;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice:hover {
            background-color: var(--bs-primary) !important;
            opacity: 0.9;
        }

        .select2-container--default .select2-selection__choice__remove {
            color: #fff !important;
            margin-right: 0.375rem;
            margin-left: -0.25rem;
        }

        /* Attribute card styles */
        .attribute-card {
            transition: all 0.3s ease;
            border: 1px solid #afb0bb;
            border-radius: 0.375rem;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .attribute-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .attribute-card.border-primary {
            border-color: #696cff !important;
            box-shadow: 0 0 0 0.25rem rgba(105, 108, 255, 0.25);
        }

        .attribute-value-pill {
            display: inline-flex;
            align-items: center;
            transition: all 0.2s ease;
            cursor: default;
            border-radius: 50rem;
            padding: 0.35rem 0.65rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            font-size: 0.875rem;
        }

        .attribute-value-pill:hover {
            background-color: #e9ecef !important;
            border-color: #dee2e6;
        }

        .attribute-value-pill .btn-close {
            opacity: 0.5;
            transition: all 0.2s ease;
            margin-left: 0.5rem;
            font-size: 0.65rem;
            padding: 0.15rem;
        }

        .attribute-value-pill:hover .btn-close {
            opacity: 1;
        }

        .attribute-value-pill .btn-close:hover {
            background-color: #dc3545;
            color: white;
            opacity: 1;
        }

        #attribute-summary {
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
        }

        .attribute-value-pills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        #empty-attributes-state {
            border: 2px dashed #e9ecef !important;
            border-radius: 0.375rem;
            padding: 2rem 1rem;
            background-color: #f8f9fa;
        }

        .attribute-value-input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .add-attribute-value {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        /* Summernote custom styles */
        .note-editor.note-frame {
            border-color: #d9dee3;
            border-radius: 0.375rem;
        }

        .note-editor.note-frame .note-statusbar {
            border-bottom-right-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .note-editor .note-toolbar {
            background-color: #f8f9fa;
            border-top-right-radius: 0.375rem;
            border-top-left-radius: 0.375rem;
        }

        /* Saved Address Card Styling */
        .saved-address-card {
            transition: all 0.3s ease;
            border: 1px solid #d9dee3;
            cursor: pointer;
        }

        .saved-address-card:hover {
            border-color: #696cff;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .saved-address-card.border-primary {
            border-width: 2px;
            border-color: #696cff !important;
            background-color: rgba(105, 108, 255, 0.04);
        }

        .saved-address-card .card-body {
            padding: 1rem;
        }

        .saved-address-card .address-label {
            font-weight: 500;
            color: #566a7f;
        }

        .saved-address-card p {
            color: #697a8d;
            margin-bottom: 0.25rem;
        }

        /* Address selection form */
        .select-address:checked+label {
            font-weight: bold;
            color: #696cff;
        }

        /* Address label input */
        .address-label-input {
            border-radius: 4px;
            height: 30px;
            font-size: 0.75rem;
        }
    </style>
@endpush

@section('content')
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Edit Produk: {{ $product->name }}</h5>
                <div>
                    {{-- <button type="button" class="btn btn-outline-secondary me-2" href="{{ route('data-master.product.index') }}">
                        <i class='bx bx-arrow-back me-1'></i> Kembali
                    </button> --}}
                    <a class="btn btn-outline-secondary me-2" href="{{ route('data-master.product.index') }}">
                        <i class='bx bx-arrow-back me-1'></i> Kembali
                    </a>
                    <button type="button" id="submitBtn" class="btn btn-primary">
                        <i class='bx bx-save me-1'></i> Simpan Perubahan
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form id="productForm" action="{{ route('data-master.product.update', $product->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    @if ($errors->any())
                        <div class="alert alert-danger mb-4">
                            <div class="d-flex align-items-start">
                                <div class="me-2">
                                    <i class='bx bx-error-circle fs-4'></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">Ada kesalahan pada form</h5>
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <ul class="nav nav-tabs mb-4" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" href="#basic-info" role="tab">
                                <i class='bx bx-info-circle me-1'></i> Informasi Dasar
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="media-tab" data-bs-toggle="tab" href="#media" role="tab">
                                <i class='bx bx-image me-1'></i> Media
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="pricing-tab" data-bs-toggle="tab" href="#pricing" role="tab">
                                <i class='bx bx-dollar-circle me-1'></i> Harga
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="shipping-tab" data-bs-toggle="tab" href="#shipping" role="tab">
                                <i class='bx bx-package me-1'></i> Pengiriman
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="other-tab" data-bs-toggle="tab" href="#other" role="tab">
                                <i class='bx bx-cog me-1'></i> Lainnya
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content">
                        <!-- Basic Info Tab -->
                        @include('data-master.product.partials.basic-info')

                        <!-- Media Tab -->
                        @include('data-master.product.partials.media')

                        <!-- Pricing Tab -->
                        @include('data-master.product.partials.pricing')

                        <!-- Shipping Tab -->
                        @include('data-master.product.partials.shipping')

                        <!-- Other Tab -->
                        @include('data-master.product.partials.other')
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.11/dist/clipboard.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slugify@1.6.6/slugify.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.js"></script>

    @include('data-master.product.scripts.product-variants-globals')
    @include('data-master.product.scripts.product-form')
    @include('data-master.product.scripts.product-attributes')
    @include('data-master.product.scripts.product-variants')
    @include('data-master.product.scripts.product-variants-edit')
    @include('data-master.product.scripts.product-variants-improvements')
    @include('data-master.product.scripts.product-shipping')
    @include('data-master.product.scripts.product-images-edit')
    {{-- @include('data-master.product.scripts.product-images') --}}
    @include('data-master.product.scripts.product-utilities')
    @include('data-master.product.scripts.product-wholesale')
    @include('data-master.product.scripts.product-other')
@endpush
