<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SyncCprDataJob;
use App\Models\User;

class SyncCprDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cpr:sync {--account-id= : Specific account ID to sync} {--user-id= : Specific user ID to sync} {--queue : Run in background queue}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync CPR data from Facebook Ads API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $accountId = $this->option('account-id');
        $userId = $this->option('user-id');
        $useQueue = $this->option('queue');

        $this->info('Starting CPR data synchronization...');

        try {
            if ($useQueue) {
                // Dispatch to queue
                SyncCprDataJob::dispatch($accountId, $userId);
                $this->info('CPR sync job dispatched to queue successfully.');
            } else {
                // Run synchronously
                $job = new SyncCprDataJob($accountId, $userId);
                $job->handle(
                    app(\App\Services\Facebook\FacebookAdService::class),
                    app(\App\Services\MonitoringCpr\DataMonitoringCprService::class)
                );
                $this->info('CPR data synchronized successfully.');
            }

            // Show summary
            if ($accountId) {
                $this->info("Synced data for account: {$accountId}");
            } elseif ($userId) {
                $user = User::find($userId);
                if ($user && $user->fbads_id) {
                    $accounts = explode(',', $user->fbads_id);
                    $this->info("Synced data for user {$userId} accounts: " . implode(', ', $accounts));
                }
            } else {
                $totalUsers = User::whereNotNull('fbads_id')->count();
                $this->info("Synced data for all {$totalUsers} users with Facebook Ads access.");
            }

        } catch (\Exception $e) {
            $this->error('Failed to sync CPR data: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
