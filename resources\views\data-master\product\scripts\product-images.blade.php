<script>
    $(document).ready(function() {
        // Track selected images (not yet uploaded)
        let selectedImages = [];
        let primaryImageIndex = null;

        // Update empty state initially
        updateImagesEmptyState();

        // Function to update hidden inputs with all selected image files
        function updateImageInputs() {
            try {
                // Clear the container first
                $('#all-images-container').empty();

                // Add information about which image is primary
                if (primaryImageIndex !== null && primaryImageIndex >= 0 && primaryImageIndex < selectedImages.length) {
                    $('#primary_image').val(primaryImageIndex.toString());
                } else if (selectedImages.length > 0) {
                    // Set first image as primary by default
                    $('#primary_image').val('0');
                } else {
                    $('#primary_image').val('');
                }

                // Update image count badge
                $('#images-count').text(selectedImages.length + ' gambar');
            } catch (e) {
                console.error('Error updating image inputs:', e);
            }
        }

        // Function to update image previews
        function updateImagePreviews() {
            try {
                // Clear the existing previews
                $('#preview-container').empty();

                // Add preview for each selected image
                if (selectedImages && selectedImages.length > 0) {
                    selectedImages.forEach((image, index) => {
                        const isPrimary = primaryImageIndex === index;
                        const previewHtml = `
                            <div class="image-preview" data-id="${index}">
                                <img src="${image.preview}" alt="Preview">
                                ${isPrimary ? '<span class="primary-badge">Utama</span>' : ''}
                                <div class="controls">
                                    <button type="button" class="set-primary-btn" title="Jadikan Gambar Utama">
                                        <i class="bx ${isPrimary ? 'bxs-star' : 'bx-star'}"></i>
                                    </button>
                                    <button type="button" class="delete-btn" title="Hapus Gambar">
                                        <i class="bx bx-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                        $('#preview-container').append(previewHtml);
                    });

                    // Update empty state
                    updateImagesEmptyState();
                }
            } catch (e) {
                console.error('Error updating image previews:', e);
            }
        }

        // Function to update empty state visibility
        function updateImagesEmptyState() {
            if (selectedImages.length === 0) {
                $('#empty-images-state').show();
                $('#preview-container').hide();
            } else {
                $('#empty-images-state').hide();
                $('#preview-container').show();
            }
        }

        // Handle remove button clicks
        $('#preview-container').on('click', '.delete-btn', function() {
            try {
                const imagePreview = $(this).closest('.image-preview');
                const imageId = parseInt(imagePreview.data('id'));

                // Remove from our array
                if (!isNaN(imageId) && imageId >= 0 && imageId < selectedImages.length) {
                    selectedImages.splice(imageId, 1);

                    // Adjust primary image index if needed
                    if (primaryImageIndex === imageId) {
                        primaryImageIndex = selectedImages.length > 0 ? 0 : null;
                    } else if (primaryImageIndex > imageId) {
                        primaryImageIndex--;
                    }

                    // Update preview and hidden inputs
                    updateImagePreviews();
                    updateImageInputs();
                    updateImagesEmptyState();
                }
            } catch (e) {
                console.error('Error removing image:', e);
            }
        });

        // Handle set primary button clicks
        $('#preview-container').on('click', '.set-primary-btn', function() {
            try {
                const imagePreview = $(this).closest('.image-preview');
                const imageId = parseInt(imagePreview.data('id'));

                // Set as primary
                primaryImageIndex = imageId;

                // Update preview and hidden inputs
                updateImagePreviews();
                updateImageInputs();
            } catch (e) {
                console.error('Error setting primary image:', e);
            }
        });

        // Initialize Dropzone
        let myDropzone = new Dropzone("#productImages", {
            url: "/", // Dummy URL, we won't be uploading directly
            paramName: "file",
            maxFilesize: 2, // MB
            acceptedFiles: "image/jpeg,image/png,image/jpg",
            addRemoveLinks: false,
            autoProcessQueue: false, // Prevent automatic upload
            createImageThumbnails: true,
            dictDefaultMessage: "Tarik & lepas gambar di sini atau klik untuk memilih file",
            previewsContainer: false, // We'll handle previews ourselves

            // Add file to our collection when added to queue
            addedfile: function(file) {
                try {
                    // Create a temporary preview
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const newImage = {
                            file: file,
                            preview: e.target.result
                        };

                        // Add to preview array
                        selectedImages.push(newImage);

                        // Set first image as primary by default if no primary is set
                        if (primaryImageIndex === null) {
                            primaryImageIndex = 0;
                        }

                        // Update the preview
                        updateImagePreviews();
                        updateImageInputs();
                    };
                    reader.readAsDataURL(file);
                } catch (e) {
                    console.error('Error adding file:', e);
                }
            }
        });

        // Make the preview container sortable
        $("#preview-container").sortable({
            placeholder: "image-preview-placeholder",
            cursor: "grabbing",
            update: function(event, ui) {
                try {
                    // Get the new order
                    const newOrder = [];
                    $('#preview-container .image-preview').each(function() {
                        const oldIndex = parseInt($(this).data('id'));
                        if (!isNaN(oldIndex) && oldIndex >= 0 && oldIndex < selectedImages.length) {
                            newOrder.push(selectedImages[oldIndex]);
                        }
                    });

                    // Update primary image index
                    if (primaryImageIndex !== null) {
                        const primaryImage = selectedImages[primaryImageIndex];
                        primaryImageIndex = newOrder.indexOf(primaryImage);
                    }

                    // Update our array with the new order
                    selectedImages = newOrder;

                    // Update IDs on preview elements
                    $('#preview-container .image-preview').each(function(index) {
                        $(this).data('id', index);
                    });

                    // Update previews
                    updateImagePreviews();
                    updateImageInputs();
                } catch (e) {
                    console.error('Error during sort update:', e);
                }
            }
        });

        // Image preparation function - moved to window for global access
        window.prepareProductImages = function() {
            if (selectedImages.length > 0) {
                // Clear any existing file inputs
                $('#all-images-container').empty();

                // Add the files to form data using a file input for each image
                selectedImages.forEach((image, index) => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.name = `product_images[]`;
                    fileInput.style.display = 'none';

                    // Create a DataTransfer to create a FileList
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(image.file);
                    fileInput.files = dataTransfer.files;

                    // Append to the container
                    $('#all-images-container').append(fileInput);
                });

                // Add input for primary image index
                const primaryInput = document.createElement('input');
                primaryInput.type = 'hidden';
                primaryInput.name = 'primary_image_index';
                primaryInput.value = primaryImageIndex !== null ? primaryImageIndex.toString() : '0';
                $('#all-images-container').append(primaryInput);
            }
        };

        // Expose myDropzone to the global scope for access from other scripts
        window.productDropzone = myDropzone;
    });
</script>
