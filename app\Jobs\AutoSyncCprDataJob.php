<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\Facebook\FacebookAdService;
use App\Services\MonitoringCpr\DataMonitoringCprService;
use App\Models\User;
use Carbon\Carbon;

class AutoSyncCprDataJob implements ShouldQueue
{
    use Queueable;

    protected $forceSync;
    protected $accountIds;

    /**
     * Create a new job instance.
     */
    public function __construct($forceSync = false, $accountIds = null)
    {
        $this->forceSync = $forceSync;
        $this->accountIds = $accountIds;
    }

    /**
     * Execute the job.
     */
    public function handle(FacebookAdService $facebookAdService, DataMonitoringCprService $dataMonitoringCprService): void
    {
        try {
            Log::info('Starting auto CPR data sync job', [
                'force_sync' => $this->forceSync,
                'account_ids' => $this->accountIds
            ]);

            // Check if we should skip this sync (rate limiting)
            if (!$this->forceSync && $this->shouldSkipSync()) {
                Log::info('Skipping auto sync due to rate limiting');
                return;
            }

            // Get accounts to sync
            $accountsToSync = $this->getAccountsToSync();
            
            if (empty($accountsToSync)) {
                Log::info('No accounts found for auto sync');
                return;
            }

            $syncResults = [];
            $totalSynced = 0;
            $totalErrors = 0;

            foreach ($accountsToSync as $accountData) {
                try {
                    $result = $this->syncAccountData(
                        $accountData['id'], 
                        $accountData['name'],
                        $facebookAdService, 
                        $dataMonitoringCprService
                    );
                    
                    $syncResults[] = $result;
                    
                    if ($result['success']) {
                        $totalSynced += $result['campaigns_synced'] ?? 0;
                    } else {
                        $totalErrors++;
                    }
                    
                    // Small delay between accounts to avoid rate limiting
                    sleep(2);
                    
                } catch (\Exception $e) {
                    Log::error('Error syncing account', [
                        'account_id' => $accountData['id'],
                        'error' => $e->getMessage()
                    ]);
                    $totalErrors++;
                }
            }

            // Update sync status in cache
            $this->updateSyncStatus($syncResults, $totalSynced, $totalErrors);

            Log::info('Auto CPR sync completed', [
                'total_accounts' => count($accountsToSync),
                'total_campaigns_synced' => $totalSynced,
                'total_errors' => $totalErrors
            ]);

        } catch (\Exception $e) {
            Log::error('Auto CPR sync job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Update error status in cache
            Cache::put('cpr_auto_sync_status', [
                'status' => 'error',
                'message' => 'Auto sync failed: ' . $e->getMessage(),
                'last_sync' => now(),
                'next_sync' => now()->addMinutes(10)
            ], 3600);
        }
    }

    /**
     * Check if we should skip this sync based on rate limiting
     */
    private function shouldSkipSync(): bool
    {
        $lastSync = Cache::get('cpr_last_auto_sync');
        
        if (!$lastSync) {
            return false;
        }

        // Don't sync more than once every 5 minutes
        $minInterval = 5;
        $timeSinceLastSync = now()->diffInMinutes($lastSync);
        
        return $timeSinceLastSync < $minInterval;
    }

    /**
     * Get accounts that need to be synced
     */
    private function getAccountsToSync(): array
    {
        if ($this->accountIds) {
            // Sync specific accounts
            $accounts = [];
            foreach ($this->accountIds as $accountId) {
                $accounts[] = [
                    'id' => $accountId,
                    'name' => 'Account ' . $accountId
                ];
            }
            return $accounts;
        }

        // Get all users with Facebook Ads access
        $users = User::whereNotNull('fbads_id')
                    ->whereNotNull('fbads_name')
                    ->get();

        $accounts = [];
        foreach ($users as $user) {
            $idAkunArray = array_map('trim', explode(',', $user->fbads_id));
            $namaAkunArray = array_map('trim', explode(',', $user->fbads_name));

            foreach ($idAkunArray as $key => $id) {
                if (!empty($id)) {
                    $accounts[] = [
                        'id' => $id,
                        'name' => $namaAkunArray[$key] ?? 'Account ' . $id,
                        'user_id' => $user->id
                    ];
                }
            }
        }

        return array_unique($accounts, SORT_REGULAR);
    }

    /**
     * Sync data for a specific account
     */
    private function syncAccountData($accountId, $accountName, $facebookAdService, $dataMonitoringCprService): array
    {
        try {
            $fullAccountId = 'act_' . $accountId;
            
            Log::info('Syncing account data', [
                'account_id' => $accountId,
                'account_name' => $accountName
            ]);

            // Get campaigns
            $campaigns = $facebookAdService->getDataCampaigns($fullAccountId);
            if (isset($campaigns['error'])) {
                return [
                    'success' => false,
                    'account_id' => $accountId,
                    'error' => $campaigns['message'] ?? 'Failed to get campaigns'
                ];
            }

            // Get ad sets
            $adSets = $facebookAdService->getDataAdSet($fullAccountId);
            
            // Get insights
            $insights = $facebookAdService->getDataInsights($fullAccountId);

            // Process and save data (similar to existing logic)
            $campaignsSynced = $this->processAndSaveData(
                $campaigns, 
                $adSets, 
                $insights, 
                $dataMonitoringCprService
            );

            return [
                'success' => true,
                'account_id' => $accountId,
                'account_name' => $accountName,
                'campaigns_synced' => $campaignsSynced,
                'synced_at' => now()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to sync account data', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Process and save campaign data
     */
    private function processAndSaveData($campaigns, $adSets, $insights, $dataMonitoringCprService): int
    {
        if (!$campaigns || !$adSets || !$insights) {
            return 0;
        }

        // Index campaigns and insights by id
        $indexedCampaigns = [];
        foreach ($campaigns as $campaign) {
            $indexedCampaigns[$campaign['id_kampanye']] = $campaign;
        }

        $indexedInsights = [];
        foreach ($insights as $insight) {
            $indexedInsights[$insight['id_kampanye']] = $insight;
        }

        $syncedCount = 0;

        foreach ($adSets as $adSet) {
            try {
                $campaignId = $adSet['id_kampanye'] ?? null;
                $campaign = $indexedCampaigns[$campaignId] ?? null;
                $insight = $indexedInsights[$campaignId] ?? null;

                if (!$campaign || !$insight) {
                    continue;
                }

                // Prepare data for saving
                $data = [
                    'nama_set_iklan' => $adSet['nama_set_iklan'] ?? null,
                    'id_akun' => $adSet['id_akun'] ?? 0,
                    'id_kampanye' => $campaignId,
                    'anggaran_harian' => isset($adSet['anggaran_harian']) ? 
                        number_format($adSet['anggaran_harian'] / 100, 2, '.', '') : 0.00,
                    'spek_atribusi' => $adSet['spek_atribusi'] ?? null,
                    'status' => $adSet['status'] ?? null,
                    'tanggal_mulai' => isset($adSet['waktu_dibuat']) ? 
                        date('Y-m-d', strtotime($adSet['waktu_dibuat'])) : null,
                    'tanggal_berhenti' => isset($adSet['tanggal_berhenti']) ? 
                        date('Y-m-d', strtotime($adSet['tanggal_berhenti'])) : null,
                    'nama_kampanye' => $campaign['nama_kampanye'] ?? null,
                    'adlabels' => $adSet['adlabels'] ?? null,
                    'strategi_penawaran' => $adSet['strategi_bid'] ?? null,
                    'jangkauan' => $insight['jangkauan'] ?? 0,
                    'impresi' => $insight['impresi'] ?? 0,
                    'biaya_dibelanjakan' => $insight['jumlah_dibelanjakan'] ?? 0,
                    'biaya_per_klik' => $insight['biaya_per_klik'] ?? 0,
                    'biaya_perhasil' => $insight['biaya_perhasil'] ?? 0,
                    'hasil' => $insight['hasil'] ?? 0,
                ];

                $dataMonitoringCprService->updateOrCreate($data);
                $syncedCount++;

            } catch (\Exception $e) {
                Log::warning('Failed to save campaign data', [
                    'campaign_id' => $campaignId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $syncedCount;
    }

    /**
     * Update sync status in cache
     */
    private function updateSyncStatus($syncResults, $totalSynced, $totalErrors): void
    {
        $status = [
            'status' => $totalErrors > 0 ? 'partial_success' : 'success',
            'message' => $totalErrors > 0 ? 
                "Synced {$totalSynced} campaigns with {$totalErrors} errors" :
                "Successfully synced {$totalSynced} campaigns",
            'last_sync' => now(),
            'next_sync' => now()->addMinutes(10),
            'total_synced' => $totalSynced,
            'total_errors' => $totalErrors,
            'results' => $syncResults
        ];

        Cache::put('cpr_auto_sync_status', $status, 3600);
        Cache::put('cpr_last_auto_sync', now(), 3600);
    }
}
