<?php
// app/Http/Controllers/AdController.php

namespace App\Http\Controllers;

use App\Services\Facebook\FacebookInsightsService;

class AdController extends Controller
{
    protected $facebookService;

    public function __construct(FacebookInsightsService $facebookService)
    {
        $this->facebookService = $facebookService;
    }

    public function insights($adSetId)
    {
        $accessToken = 'EAAHWjIL19r4BOxgTd6FQvZCMVIFfBGczZB28MYEuwGPVB60kTbD8FBNSyhWsJPs5xxvfoM2Ix46ROZBIvFNZA2RxwVfL7tqQ6K8IgwRnk2dxdE9oDI6OSw1T2I4ZA3Dz1AWdXWJiXa1ZBQZCAFd1o2i8sb3FrUZCFb84ZBUGhZAg9k2wrpArpc77FnXXz2irnSr9EkZCTRs5Tew1xeywHew';

        try {
            $insights = $this->facebookService->getImpressionsByPlatform($adSetId, $accessToken);

            foreach ($insights as $item) {
                echo "Platform: " . $item['publisher_platform'] . " - Impressions: " . $item['impressions'] . "<br>";
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
