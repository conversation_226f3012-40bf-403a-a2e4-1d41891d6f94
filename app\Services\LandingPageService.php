<?php

namespace App\Services;

use App\Models\LandingPage;
use App\Models\LandingPageSection6;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LandingPageService
{
    /**
     * Handle image upload with optimization
     */
    public function uploadImage($file, $directory): string
    {
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        return $file->storeAs($directory, $filename, 'public');
    }

    /**
     * Handle multiple image uploads
     */
    public function handleImageUploads(Request $request, array $imageFields): array
    {
        $uploadedImages = [];
        foreach ($imageFields as $field) {
            if ($request->hasFile($field)) {
                $uploadedImages[$field] = $this->uploadImage($request->file($field), 'landing-pages');
            }
        }
        return $uploadedImages;
    }

    /**
     * Clean up uploaded images on failure
     */
    public function cleanupImages(array $imagePaths): void
    {
        foreach ($imagePaths as $imagePath) {
            if (Storage::exists($imagePath)) {
                Storage::delete($imagePath);
            }
        }
    }

    /**
     * Delete old images when updating
     */
    public function deleteOldImages(LandingPage $landingPage, array $imageFields): void
    {
        foreach ($imageFields as $field) {
            if ($landingPage->$field && Storage::exists($landingPage->$field)) {
                Storage::delete($landingPage->$field);
            }
        }
    }

    /**
     * Prepare landing page data with proper boolean handling
     */
    public function prepareLandingPageData(Request $request, array $uploadedImages = []): array
    {
        $imageFields = [
            'section_image',
            'section2_image',
            'section3_image',
            'section4_image',
            'section5_image'
        ];

        $landingPageData = $request->except($imageFields);
        $landingPageData = array_merge($landingPageData, $uploadedImages);

        // Handle checkbox values properly - convert to boolean
        $landingPageData['section3_enabled'] = $request->boolean('section3_enabled');
        $landingPageData['section4_enabled'] = $request->boolean('section4_enabled');
        $landingPageData['section5_enabled'] = $request->boolean('section5_enabled');

        // Handle product_id fields - convert empty strings to null for integer columns
        $productIdFields = [
            'product_id',
            'section2_product_id',
            'section3_product_id',
            'section4_product_id',
            'section5_product_id'
        ];

        foreach ($productIdFields as $field) {
            if (isset($landingPageData[$field]) && $landingPageData[$field] === '') {
                $landingPageData[$field] = null;
            }
        }

        // Handle array fields properly - ensure they are arrays
        if ($request->has('pixel_events')) {
            $pixelEvents = $request->input('pixel_events');
            $landingPageData['pixel_events'] = is_array($pixelEvents) ? $pixelEvents : [];
        }

        if ($request->has('pixel_event_parameters')) {
            $pixelEventParameters = $request->input('pixel_event_parameters');
            $landingPageData['pixel_event_parameters'] = is_array($pixelEventParameters) ? $pixelEventParameters : [];
        }

        return $landingPageData;
    }

    /**
     * Clear optional section data if sections are disabled
     */
    public function clearDisabledSections(array &$landingPageData): void
    {
        $sections = [3, 4, 5];

        foreach ($sections as $section) {
            $enabledKey = "section{$section}_enabled";

            if (!$landingPageData[$enabledKey]) {
                $landingPageData["section{$section}_product_id"] = null;
                $landingPageData["section{$section}_title"] = null;
                $landingPageData["section{$section}_sub_title"] = null;
                $landingPageData["section{$section}_content"] = null;

                // Only clear image on create, not update
                if (!isset($landingPageData['id'])) {
                    $landingPageData["section{$section}_image"] = null;
                }
            }
        }
    }

    /**
     * Extract Section 6 data from request with proper validation
     */
    public function extractSection6Data(Request $request): array
    {
        $section6Fields = [
            'section6_product_id' => 'product_id',
            'section6_background_color' => 'background_color',
            'section6_customer_name_label' => 'customer_name_label',
            'section6_customer_name_placeholder' => 'customer_name_placeholder',
            'section6_whatsapp_label' => 'whatsapp_label',
            'section6_whatsapp_placeholder' => 'whatsapp_placeholder',
            'section6_address_label' => 'address_label',
            'section6_address_placeholder' => 'address_placeholder',
            'section6_city_label' => 'city_label',
            'section6_city_default' => 'city_default',
            'section6_notes_label' => 'notes_label',
            'section6_notes_placeholder' => 'notes_placeholder',
            'section6_shipping_label' => 'shipping_label',
            'section6_shipping_service_label' => 'shipping_service_label',
            'section6_payment_label' => 'payment_label',
            'section6_enable_bank_transfer' => 'enable_bank_transfer',
            'section6_enable_cod' => 'enable_cod',
            'section6_product_price' => 'product_price',
            'section6_shipping_cost' => 'shipping_cost',
            'section6_total_price' => 'total_price',
            'section6_order_button_text' => 'order_button_text',
            'section6_section1_button_text' => 'section1_button_text',
        ];

        $section6Data = [];
        foreach ($section6Fields as $requestField => $dbField) {
            // Handle checkbox values specially - they need to be processed even if not present
            if (in_array($requestField, ['section6_enable_bank_transfer', 'section6_enable_cod'])) {
                $section6Data[$dbField] = $request->boolean($requestField);
            } elseif ($request->has($requestField)) {
                $value = $request->input($requestField);

                // Handle product_id specially - convert empty string to null for integer column
                if ($requestField === 'section6_product_id' && $value === '') {
                    $section6Data[$dbField] = null;
                } else {
                    $section6Data[$dbField] = $value;
                }
            }
        }

        return $section6Data;
    }

    /**
     * Create landing page with transaction
     */
    public function createLandingPage(Request $request, int $userId): array
    {
        try {
            DB::beginTransaction();

            $imageFields = [
                'section_image',
                'section2_image',
                'section3_image',
                'section4_image',
                'section5_image'
            ];

            // Handle image uploads
            $uploadedImages = $this->handleImageUploads($request, $imageFields);

            // Prepare landing page data
            $landingPageData = $this->prepareLandingPageData($request, $uploadedImages);
            $this->clearDisabledSections($landingPageData);

            // Set user and slug
            $landingPageData['user_id'] = $userId;
            $landingPageData['slug'] = Str::slug($request->name . '-' . time());

            $landingPage = LandingPage::create($landingPageData);

            // Create Section 6 - always create with proper boolean handling
            $section6Data = $this->extractSection6Data($request);
            $section6Data['landing_page_id'] = $landingPage->id;
            LandingPageSection6::create($section6Data);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Landing page berhasil disimpan!',
                'data' => $landingPage
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            $this->cleanupImages($uploadedImages ?? []);

            return [
                'success' => false,
                'message' => 'Gagal menyimpan landing page: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update landing page with transaction
     */
    public function updateLandingPage(Request $request, LandingPage $landingPage): array
    {
        try {
            DB::beginTransaction();

            $imageFields = [
                'section_image',
                'section2_image',
                'section3_image',
                'section4_image',
                'section5_image'
            ];

            $uploadedImages = [];
            foreach ($imageFields as $field) {
                if ($request->hasFile($field)) {
                    // Delete old image if exists
                    if ($landingPage->$field && Storage::exists($landingPage->$field)) {
                        Storage::delete($landingPage->$field);
                    }
                    $uploadedImages[$field] = $this->uploadImage($request->file($field), 'landing-pages');
                }
            }

            // Prepare landing page data
            $landingPageData = $this->prepareLandingPageData($request, $uploadedImages);
            $landingPageData['id'] = $landingPage->id; // Mark as update
            $this->clearDisabledSections($landingPageData);

            $landingPageData['slug'] = Str::slug($request->name . '-' . $landingPage->id);

            $landingPage->update($landingPageData);

            // Update or create Section 6 - with proper boolean handling
            $section6Data = $this->extractSection6Data($request);
            $landingPage->section6()->updateOrCreate(
                ['landing_page_id' => $landingPage->id],
                $section6Data
            );

            DB::commit();

            return [
                'success' => true,
                'message' => 'Landing page berhasil diupdate!',
                'data' => $landingPage
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            $this->cleanupImages($uploadedImages);

            return [
                'success' => false,
                'message' => 'Gagal mengupdate landing page: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete landing page with cleanup
     */
    public function deleteLandingPage(LandingPage $landingPage): array
    {
        try {
            $imageFields = [
                'section_image',
                'section2_image',
                'section3_image',
                'section4_image',
                'section5_image'
            ];

            $this->deleteOldImages($landingPage, $imageFields);
            $landingPage->delete();

            return [
                'success' => true,
                'message' => 'Landing page berhasil dihapus!'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Gagal menghapus landing page: ' . $e->getMessage()
            ];
        }
    }
}
