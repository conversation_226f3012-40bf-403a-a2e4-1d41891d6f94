<?php

namespace App\Repositories\MonitoringCpr;

use App\Models\DataMonitoringCpr;

class DataMonitoringCprRepository
{
    /**
     * Create a new class instance.
     */
    public function __construct(protected DataMonitoringCpr $datamonitoringcprModel)
    {
    }

    public function updateOrCreate(array $data)
    {
        // Try to find existing record
        $existing = $this->datamonitoringcprModel->where('id_kampanye', $data['id_kampanye'])->first();

        if ($existing) {
            // Update existing record
            $existing->update($data);

            return $existing;
        } else {
            // Create new record
            return $this->datamonitoringcprModel->create($data);
        }
    }

    public function getDataIdUnique($dateRange, $idAkun, $status = null)
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $query = $this->datamonitoringcprModel->select([
            'nama_set_iklan',
            'id_akun',
            'id_kampanye',
            'anggaran_harian',
            'spek_atribusi',
            'status',
            'tanggal_mulai',
            'tanggal_berhenti',
            'nama_kampanye',
            'adlabels',
            'strategi_penawaran',
            'jangkauan',
            'impresi',
            'biaya_dibelanjakan',
            'biaya_per_klik',
            'biaya_perhasil',
            'hasil',
        ])
            ->whereBetween('tanggal_mulai', [$startDate, $endDate])
            ->when($idAkun, function ($q) use ($idAkun) {
                return $q->where('id_akun', $idAkun);
            })
            ->when($status, function ($q) use ($status) {
                return $q->where('status', $status);
            })
            ->orderByDesc('id')
            ->get();

        return $query;
    }

    /**
     * Get statistics for dashboard
     */
    public function getStatistics($dateRange, $idAkun, $status = null)
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        $query = $this->datamonitoringcprModel
            ->whereBetween('tanggal_mulai', [$startDate, $endDate])
            ->when($idAkun, function ($q) use ($idAkun) {
                return $q->where('id_akun', $idAkun);
            })
            ->when($status, function ($q) use ($status) {
                return $q->where('status', $status);
            });

        return [
            'total_campaigns' => $query->count(),
            'active_campaigns' => $query->where('status', 'ACTIVE')->count(),
            'total_spend' => $query->sum('biaya_dibelanjakan'),
            'total_results' => $query->sum('hasil'),
            'avg_cpr' => $query->avg('biaya_perhasil'),
        ];
    }
}
