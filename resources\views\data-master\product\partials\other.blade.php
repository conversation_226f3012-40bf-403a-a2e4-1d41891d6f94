<div class="tab-pane fade" id="other" role="tabpanel">
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">Penanggung Jawab Produk</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class='bx bx-info-circle'></i>
                Penanggung jawab produk akan menangani orderan yang masuk untuk produk ini.
                Hanya CS yang dapat menjadi penanggung jawab produk.
            </div>

            <div class="row">
                <div class="col-md-12">
                    <!-- Distribution Type Selection -->
                    <div class="mb-3">
                        <label class="form-label">Tipe Distribusi <span class="text-danger">*</span></label>
                        <select id="distribution_type" name="distribution_type" class="form-select" required>
                            <option value="equal" {{ old('distribution_type', isset($product) && $product->responsibleUsers->isNotEmpty() ? $product->responsibleUsers->first()->distribution_type : 'equal') == 'equal' ? 'selected' : '' }}>
                                Sama Rata (Equal)
                            </option>
                            <option value="percentage" {{ old('distribution_type', isset($product) && $product->responsibleUsers->isNotEmpty() ? $product->responsibleUsers->first()->distribution_type : '') == 'percentage' ? 'selected' : '' }}>
                                Persentase (Percentage)
                            </option>
                            <option value="fixed" {{ old('distribution_type', isset($product) && $product->responsibleUsers->isNotEmpty() ? $product->responsibleUsers->first()->distribution_type : '') == 'fixed' ? 'selected' : '' }}>
                                Tetap (Fixed)
                            </option>
                        </select>
                        <div class="form-text">
                            Pilih metode distribusi order ke Customer Service
                        </div>
                        @error('distribution_type')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Responsible Users Selection -->
                    <div class="mb-3">
                        <label class="form-label">Pilih Penanggung Jawab <span class="text-danger">*</span></label>
                        <select id="responsible_users" name="responsible_users[]" class="form-select" multiple required>
                            @foreach ($users->where('level_user', 3) as $user)
                                <option value="{{ $user->id }}"
                                        @selected(in_array($user->id, old('responsible_users', isset($product) ? $product->responsibleUsers->pluck('user_id')->toArray() : [])))
                                        data-distribution-type="{{ isset($product) ? $product->responsibleUsers->where('user_id', $user->id)->first()->distribution_type ?? '' : '' }}"
                                        data-distribution-value="{{ isset($product) ? $product->responsibleUsers->where('user_id', $user->id)->first()->distribution_value ?? '' : '' }}">
                                    {{ $user->name }} - {{ $user->email }}
                                    @if (isset($product) && $product->responsibleUsers->where('user_id', $user->id)->isNotEmpty())
                                        @php $ru = $product->responsibleUsers->where('user_id', $user->id)->first(); @endphp
                                        ({{ $ru->distribution_type == 'percentage' ? $ru->distribution_value . '%' : ($ru->distribution_type == 'fixed' ? $ru->distribution_value . ' order' : 'equal') }})
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        <div class="form-text">
                            Pilih satu atau lebih penanggung jawab yang akan menangani orderan produk ini
                        </div>
                        @error('responsible_users')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Distribution Values Container -->
                    <div id="distribution_values_container" class="card mb-3" style="display: none;">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <h5 class="card-title mb-0">Atur Nilai Distribusi</h5>
                                @if (isset($product) && $product->responsibleUsers->isNotEmpty())
                                    <span class="badge bg-info ms-2">
                                        <i class="bx bx-info-circle"></i> Mode Edit
                                    </span>
                                @endif
                            </div>

                            @if (isset($product) && $product->responsibleUsers->isNotEmpty())
                                <div class="alert alert-info mb-3">
                                    <i class="bx bx-lightbulb"></i>
                                    <strong>Nilai yang ada:</strong> Sistem akan menampilkan nilai distribusi yang sudah tersimpan untuk setiap penanggung jawab.
                                </div>
                            @endif

                            <div id="distribution_values_inputs">
                                <!-- Dynamic inputs will be added here by jQuery -->
                            </div>
                            <div id="distribution_errors" class="mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
