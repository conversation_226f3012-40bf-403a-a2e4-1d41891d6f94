# Enhanced Monitoring CPR - Real-time Facebook Ads Monitoring

## 🚀 Fitur Baru

### ✨ Real-time Updates
- **Auto-refresh dengan interval yang dapat disesuaikan** (10 detik - 5 menit)
- **Countdown timer** menunjukkan waktu refresh berikutnya
- **Notifikasi toast** untuk setiap update data
- **Status sync real-time** dengan indikator visual

### 🎨 Enhanced UI/UX
- **Modern card design** dengan hover effects dan shadows
- **Loading overlays** yang smooth untuk table dan statistics
- **Color-coded CPR values** dengan icons untuk trend indication
- **Enhanced table styling** dengan better typography
- **Responsive notifications** dengan auto-hide functionality

### 🔄 Automated Data Collection
- **Background auto-sync** setiap 10 menit selama jam kerja
- **Smart rate limiting** untuk menghindari API limits
- **Error handling** dan retry mechanism
- **Scheduled jobs** untuk sync otomatis

### 📊 Improved Statistics
- **Animated number updates** dengan fade effects
- **Change indicators** menunjukkan perubahan dari update sebelumnya
- **Better currency formatting** untuk Rupiah
- **Real-time statistics** yang update bersamaan dengan data

## 🛠️ Setup dan Instalasi

### 1. Database Migration
```bash
# Jalankan migration yang sudah ada
php artisan migrate

# Pastikan indexes sudah terpasang untuk performa optimal
php artisan migrate --path=database/migrations/2025_01_20_000001_add_indexes_to_data_monitoring_cpr_table.php
```

### 2. Queue Configuration
```bash
# Setup queue untuk background jobs
php artisan queue:table
php artisan migrate

# Jalankan queue worker
php artisan queue:work --queue=default --tries=3
```

### 3. Scheduled Jobs
```bash
# Tambahkan ke crontab untuk auto-sync
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1

# Atau jalankan manual untuk testing
php artisan cpr:auto-sync --queue
```

## 📋 Penggunaan

### Auto-refresh Controls
- **Toggle Auto-refresh**: Klik tombol timer untuk mengaktifkan/menonaktifkan
- **Interval Setting**: Pilih interval refresh dari dropdown (10s, 30s, 1m, 5m)
- **Manual Refresh**: Klik tombol refresh hijau untuk update manual
- **Sync Data**: Klik tombol sync biru untuk sinkronisasi dengan Facebook API

### Real-time Monitoring
- **Status Indicator**: Menunjukkan status sinkronisasi (Success/Syncing/Error)
- **Countdown Timer**: Menampilkan waktu hingga refresh berikutnya
- **Last Updated**: Timestamp update terakhir dengan format yang user-friendly
- **Notifications**: Toast notifications untuk setiap aksi dan update

### Enhanced Filtering
- **Account Selection**: Pilih akun iklan untuk monitoring
- **Date Range**: Filter berdasarkan rentang tanggal
- **Campaign Status**: Filter berdasarkan status kampanye (Active/Paused/Archived)
- **Real-time Filtering**: Filter langsung update data tanpa reload halaman

## 🔧 Command Line Interface

### Auto-sync Commands
```bash
# Auto-sync semua akun
php artisan cpr:auto-sync

# Force sync (abaikan rate limiting)
php artisan cpr:auto-sync --force

# Sync akun tertentu
php artisan cpr:auto-sync --accounts=123456,789012

# Jalankan di background queue
php artisan cpr:auto-sync --queue

# Lihat status sync
php artisan cpr:auto-sync --force
```

### Legacy Commands (masih tersedia)
```bash
# Sync manual dengan command lama
php artisan cpr:sync

# Sync dengan queue
php artisan cpr:sync --queue
```

## 📊 API Endpoints

### Sync Status
```javascript
// GET /ads/monitoring-cpr/sync-status
// Response:
{
  "success": true,
  "data": {
    "status": "success",
    "message": "Successfully synced 25 campaigns",
    "last_sync": "2025-01-20T10:30:00Z",
    "next_sync": "2025-01-20T10:40:00Z",
    "total_synced": 25,
    "total_errors": 0
  }
}
```

### Trigger Manual Sync
```javascript
// POST /ads/monitoring-cpr/trigger-sync
// Body: { "account_id": "123456" }
// Response:
{
  "success": true,
  "message": "Sync job dispatched successfully"
}
```

## 🎯 Performance Optimizations

### Database Indexing
- **Composite indexes** untuk query patterns yang umum
- **Individual indexes** untuk filtering dan sorting
- **Optimized queries** dengan proper WHERE clauses

### Caching Strategy
- **Sync status caching** untuk menghindari database hits
- **Rate limiting cache** untuk API calls
- **Statistics caching** untuk dashboard performance

### Frontend Optimizations
- **Debounced filtering** untuk menghindari excessive requests
- **Smart refresh** hanya ketika data berubah
- **Efficient DOM updates** dengan minimal redraws

## 🔍 Monitoring dan Debugging

### Log Files
```bash
# Auto-sync logs
tail -f storage/logs/cpr-auto-sync.log

# Full sync logs
tail -f storage/logs/cpr-full-sync.log

# Laravel logs
tail -f storage/logs/laravel.log
```

### Cache Inspection
```bash
# Lihat status sync di cache
php artisan tinker
>>> Cache::get('cpr_auto_sync_status')
>>> Cache::get('cpr_last_auto_sync')
```

### Queue Monitoring
```bash
# Lihat failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear failed jobs
php artisan queue:flush
```

## 🚨 Troubleshooting

### Common Issues

1. **Auto-refresh tidak berjalan**
   - Pastikan JavaScript tidak ada error di console
   - Check network connectivity
   - Verify API endpoints accessible

2. **Sync gagal**
   - Check Facebook API credentials
   - Verify account permissions
   - Check rate limiting status

3. **Data tidak update**
   - Check queue worker running
   - Verify scheduled jobs active
   - Check database connectivity

4. **Performance issues**
   - Monitor database query performance
   - Check cache hit rates
   - Verify proper indexing

### Debug Mode
```javascript
// Enable debug mode di browser console
localStorage.setItem('cpr_debug', 'true');
// Reload halaman untuk melihat debug info
```

## 📈 Future Enhancements

### Planned Features
- **WebSocket integration** untuk real-time updates
- **Advanced charts** dengan Chart.js
- **Export functionality** untuk data reporting
- **Alert system** untuk threshold monitoring
- **Mobile responsive** improvements
- **Dark mode** support

### API Improvements
- **GraphQL endpoint** untuk flexible data fetching
- **Webhook support** untuk Facebook API changes
- **Bulk operations** untuk multiple accounts
- **Advanced filtering** dengan complex queries

## 🤝 Contributing

Untuk berkontribusi pada pengembangan modul ini:

1. Fork repository
2. Buat feature branch
3. Implement changes dengan tests
4. Submit pull request dengan dokumentasi

## 📞 Support

Untuk bantuan teknis atau bug reports:
- Create issue di repository
- Contact development team
- Check documentation dan logs
