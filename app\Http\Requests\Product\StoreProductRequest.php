<?php

namespace App\Http\Requests\Product;

use App\Helpers\CurrencyHelper;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     * Convert Indonesian formatted currency values to standard format.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Process price fields
        $priceFields = ['regular_price', 'discount_price', 'cost_price'];

        foreach ($priceFields as $field) {
            if ($this->has($field) && is_string($this->input($field))) {
                try {
                    $parsedValue = CurrencyHelper::parseIndonesianNumber($this->input($field));
                    Log::debug("Parsed {$field}: " . $this->input($field) . " -> {$parsedValue}");
                    $this->merge([$field => $parsedValue]);
                } catch (\Exception $e) {
                    Log::error("Error parsing {$field}: " . $e->getMessage());
                }
            }
        }

        // Process variant prices
        if ($this->has('variants') && is_array($this->input('variants'))) {
            $variants = $this->input('variants');

            foreach ($variants as $key => $variant) {
                if (isset($variant['price']) && is_string($variant['price'])) {
                    $variants[$key]['price'] = CurrencyHelper::parseIndonesianNumber($variant['price']);
                }

                if (isset($variant['discount_price']) && is_string($variant['discount_price'])) {
                    $variants[$key]['discount_price'] = CurrencyHelper::parseIndonesianNumber($variant['discount_price']);
                }
            }

            $this->merge(['variants' => $variants]);
        }

        // Process wholesale prices
        if ($this->has('wholesale_prices') && is_array($this->input('wholesale_prices'))) {
            $wholesalePrices = $this->input('wholesale_prices');

            foreach ($wholesalePrices as $key => $price) {
                if (isset($price['price']) && is_string($price['price'])) {
                    $wholesalePrices[$key]['price'] = CurrencyHelper::parseIndonesianNumber($price['price']);
                }
            }

            $this->merge(['wholesale_prices' => $wholesalePrices]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Basic Info
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'advanced_description' => 'required|string',
            'category_id' => 'required|exists:product_categories,id',
            'product_code' => 'nullable|string|max:50|unique:products,product_code',
            'checkout_url' => 'nullable|string|max:255',
            'status' => 'required|in:draft,active',
            'primary_image' => 'nullable|boolean',
            'primary_image_index' => 'nullable|numeric',
            'has_variants' => 'nullable|boolean',

            // Pricing
            'regular_price' => 'required_if:has_variants,0|numeric|min:0|max:1000000000',
            'cost_price' => 'nullable|numeric|min:0|max:1000000000',
            'has_promo' => 'nullable|boolean',
            'discount_price' => 'nullable|numeric|min:0|max:1000000000',
            'has_wholesale' => 'nullable|boolean',

            // Dimensions & Weight
            'weight' => 'required|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',

            // Shipping & Payment Methods
            'shipping_methods' => 'required|array|min:1',
            'shipping_methods.*' => 'exists:shipping_methods,id',
            'payment_methods' => 'required|array|min:1',
            'payment_methods.*' => 'exists:payment_methods,id',

            // Address Selection
            'address_selection_type' => 'required|in:saved,new',
            'selected_address_id' => 'required_if:address_selection_type,saved|exists:shipping_addresses,id',

            // Responsible Users
            // 'responsible_users' => 'required|array|min:1',
            // 'responsible_users.*' => 'exists:users,id',
            'distribution_type' => [
                'required',
                Rule::in(['equal', 'percentage', 'fixed'])
            ],
            'responsible_users' => [
                'required',
                'array',
                'min:1'
            ],
            'responsible_users.*' => [
                'exists:users,id,level_user,3' // Pastikan user adalah customer service (level 3)
            ],
            'distribution_values' => [
                'required_if:distribution_type,percentage,fixed',
                'array',
                function ($attribute, $value, $fail) {
                    // Validasi khusus berdasarkan tipe distribusi
                    $type = $this->input('distribution_type');

                    if ($type === 'percentage') {
                        $total = array_sum($value);
                        if (abs($total - 100) > 0.01) {
                            $fail('Total persentase distribusi harus tepat 100%.');
                        }

                        foreach ($value as $userId => $percentage) {
                            if ($percentage <= 0 || $percentage > 100) {
                                $fail("Persentase untuk user ID {$userId} harus antara 0-100%.");
                            }
                        }
                    }

                    if ($type === 'fixed') {
                        foreach ($value as $userId => $fixedValue) {
                            if ($fixedValue < 1) {
                                $fail("Nilai tetap untuk user ID {$userId} harus minimal 1.");
                            }
                        }
                    }
                }
            ],

            'distribution_values.*' => [
                'numeric',
                'min:0.01'
            ],

            // Product Images
            'product_images' => 'required|array',
            'product_images.*' => 'image|mimes:jpeg,png,jpg|max:2048',

            // Conditional validation for wholesale prices
            'wholesale_prices' => 'required_if:has_wholesale,1|array|min:1',
            'wholesale_prices.*.min_quantity' => 'required_if:has_wholesale,1|integer|min:1',
            'wholesale_prices.*.price' => 'required_if:has_wholesale,1|numeric|min:0',

            // Conditional validation for shipping origins
            'shipping_origins' => 'required_if:address_selection_type,new|array|min:1',
            'shipping_origins.*.name' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.address' => 'required_if:address_selection_type,new|string',
            'shipping_origins.*.city' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.state' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.country' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.postal_code' => 'required_if:address_selection_type,new|string|max:20',

            // Conditional validation for variants
            'variant_attributes' => 'required_if:has_variants,1|array|min:1',
            'variant_values' => 'required_if:has_variants,1|array',
            'variants' => 'required_if:has_variants,1|array|min:1',
            'variants.*.combination' => 'required_if:has_variants,1|string',
            'variants.*.price' => 'required|numeric|min:0',
            'variants.*.discount_price' => 'nullable|numeric|min:0',
            'variants.*.variant_code' => 'nullable|string|max:50|distinct',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // Informasi Dasar
            'name.required' => 'Nama produk wajib diisi.',
            'name.string' => 'Nama produk harus berupa teks.',
            'name.max' => 'Nama produk maksimal 255 karakter.',
            'description.required' => 'Deskripsi produk wajib diisi.',
            'description.string' => 'Deskripsi produk harus berupa teks.',
            'category_id.required' => 'Kategori produk wajib dipilih.',
            'category_id.exists' => 'Kategori produk tidak valid.',
            'product_code.string' => 'Kode produk harus berupa teks.',
            'product_code.max' => 'Kode produk maksimal 50 karakter.',
            'product_code.unique' => 'Kode produk sudah digunakan.',
            'checkout_url.string' => 'URL checkout harus berupa teks.',
            'checkout_url.max' => 'URL checkout maksimal 255 karakter.',
            'status.required' => 'Status produk wajib dipilih.',
            'status.in' => 'Status produk tidak valid.',
            'primary_image.boolean' => 'Status gambar utama harus benar atau salah.',
            'primary_image_index.numeric' => 'Indeks gambar utama harus angka.',
            'has_variants.boolean' => 'Status variasi harus benar atau salah.',

            // Harga
            'regular_price.required_if' => 'Harga normal wajib diisi untuk produk tanpa variasi.',
            'regular_price.numeric' => 'Harga normal harus angka.',
            'regular_price.min' => 'Harga normal minimal 0.',
            'regular_price.max' => 'Harga normal maksimal 1 miliar.',
            'cost_price.numeric' => 'Harga pokok harus angka.',
            'cost_price.min' => 'Harga pokok minimal 0.',
            'cost_price.max' => 'Harga pokok maksimal 1 miliar.',
            'has_promo.boolean' => 'Status promo harus benar atau salah.',
            'discount_price.numeric' => 'Harga promo harus angka.',
            'discount_price.min' => 'Harga promo minimal 0.',
            'discount_price.max' => 'Harga promo maksimal 1 miliar.',
            'has_wholesale.boolean' => 'Status grosir harus benar atau salah.',

            // Dimensi & Berat
            'weight.required' => 'Berat produk wajib diisi.',
            'weight.numeric' => 'Berat produk harus angka.',
            'weight.min' => 'Berat produk minimal 0.',
            'length.numeric' => 'Panjang produk harus angka.',
            'length.min' => 'Panjang produk minimal 0.',
            'width.numeric' => 'Lebar produk harus angka.',
            'width.min' => 'Lebar produk minimal 0.',
            'height.numeric' => 'Tinggi produk harus angka.',
            'height.min' => 'Tinggi produk minimal 0.',

            // Pengiriman & Pembayaran
            'shipping_methods.required' => 'Pilih minimal 1 metode pengiriman.',
            'shipping_methods.array' => 'Format metode pengiriman tidak valid.',
            'shipping_methods.min' => 'Pilih minimal 1 metode pengiriman.',
            'shipping_methods.*.exists' => 'Metode pengiriman tidak valid.',
            'payment_methods.required' => 'Pilih minimal 1 metode pembayaran.',
            'payment_methods.array' => 'Format metode pembayaran tidak valid.',
            'payment_methods.min' => 'Pilih minimal 1 metode pembayaran.',
            'payment_methods.*.exists' => 'Metode pembayaran tidak valid.',

            // Alamat
            'address_selection_type.required' => 'Jenis alamat wajib dipilih.',
            'address_selection_type.in' => 'Jenis alamat tidak valid.',
            'selected_address_id.required_if' => 'Alamat pengiriman wajib dipilih.',
            'selected_address_id.exists' => 'Alamat pengiriman tidak valid.',

            // Penanggung Jawab
            // 'responsible_users.required' => 'Pilih minimal 1 penanggung jawab.',
            // 'responsible_users.array' => 'Format penanggung jawab tidak valid.',
            // 'responsible_users.min' => 'Pilih minimal 1 penanggung jawab.',
            // 'responsible_users.*.exists' => 'Penanggung jawab tidak valid.',
            'distribution_type.required' => 'Tipe distribusi wajib dipilih.',
            'distribution_type.in' => 'Tipe distribusi tidak valid.',
            'responsible_users.required' => 'Pilih minimal satu penanggung jawab.',
            'responsible_users.array' => 'Format penanggung jawab tidak valid.',
            'responsible_users.min' => 'Pilih minimal satu penanggung jawab.',
            'responsible_users.*.exists' => 'Salah satu penanggung jawab tidak valid.',
            'distribution_values.required_if' => 'Nilai distribusi wajib diisi untuk tipe ini.',
            'distribution_values.array' => 'Format nilai distribusi tidak valid.',
            'distribution_values.*.numeric' => 'Nilai distribusi harus berupa angka.',
            'distribution_values.*.min' => 'Nilai distribusi minimal :min.',

            // Gambar Produk
            'product_images.required' => 'Unggah minimal 1 gambar produk.',
            'product_images.array' => 'Format gambar produk tidak valid.',
            'product_images.*.image' => 'File harus berupa gambar.',
            'product_images.*.mimes' => 'Format gambar harus jpeg, png, atau jpg.',
            'product_images.*.max' => 'Ukuran gambar maksimal 2MB.',

            // Harga Grosir
            'wholesale_prices.required_if' => 'Isi minimal 1 harga grosir.',
            'wholesale_prices.array' => 'Format harga grosir tidak valid.',
            'wholesale_prices.min' => 'Isi minimal 1 harga grosir.',
            'wholesale_prices.*.min_quantity.required_if' => 'Jumlah minimal grosir wajib diisi.',
            'wholesale_prices.*.min_quantity.integer' => 'Jumlah minimal harus bilangan bulat.',
            'wholesale_prices.*.min_quantity.min' => 'Jumlah minimal minimal 1.',
            'wholesale_prices.*.price.required_if' => 'Harga grosir wajib diisi.',
            'wholesale_prices.*.price.numeric' => 'Harga grosir harus angka.',
            'wholesale_prices.*.price.min' => 'Harga grosir minimal 0.',
            'wholesale_prices.*.price.max' => 'Harga grosir maksimal 1 miliar.',

            // Alamat Pengiriman Baru
            'shipping_origins.required_if' => 'Isi minimal 1 alamat pengiriman.',
            'shipping_origins.array' => 'Format alamat pengiriman tidak valid.',
            'shipping_origins.min' => 'Isi minimal 1 alamat pengiriman.',
            'shipping_origins.*.name.required_if' => 'Nama pengirim wajib diisi.',
            'shipping_origins.*.name.string' => 'Nama pengirim harus teks.',
            'shipping_origins.*.name.max' => 'Nama pengirim maksimal 255 karakter.',
            'shipping_origins.*.address.required_if' => 'Alamat wajib diisi.',
            'shipping_origins.*.address.string' => 'Alamat harus teks.',
            'shipping_origins.*.city.required_if' => 'Kota wajib diisi.',
            'shipping_origins.*.city.string' => 'Kota harus teks.',
            'shipping_origins.*.city.max' => 'Kota maksimal 255 karakter.',
            'shipping_origins.*.state.required_if' => 'Provinsi wajib diisi.',
            'shipping_origins.*.state.string' => 'Provinsi harus teks.',
            'shipping_origins.*.state.max' => 'Provinsi maksimal 255 karakter.',
            'shipping_origins.*.country.required_if' => 'Negara wajib diisi.',
            'shipping_origins.*.country.string' => 'Negara harus teks.',
            'shipping_origins.*.country.max' => 'Negara maksimal 255 karakter.',
            'shipping_origins.*.postal_code.required_if' => 'Kode pos wajib diisi.',
            'shipping_origins.*.postal_code.string' => 'Kode pos harus teks.',
            'shipping_origins.*.postal_code.max' => 'Kode pos maksimal 20 karakter.',

            // Variasi Produk
            'variant_attributes.required_if' => 'Atribut variasi wajib diisi.',
            'variant_attributes.array' => 'Format atribut variasi tidak valid.',
            'variant_attributes.min' => 'Isi minimal 1 atribut variasi.',
            'variant_values.required_if' => 'Nilai variasi wajib diisi.',
            'variant_values.array' => 'Format nilai variasi tidak valid.',
            'variants.required_if' => 'Kombinasi variasi wajib diisi.',
            'variants.array' => 'Format variasi tidak valid.',
            'variants.min' => 'Isi minimal 1 kombinasi variasi.',
            'variants.*.combination.required_if' => 'Kombinasi variasi wajib diisi.',
            'variants.*.combination.string' => 'Kombinasi variasi harus teks.',
            'variants.*.price.required' => 'Harga variasi wajib diisi.',
            'variants.*.price.numeric' => 'Harga variasi harus angka.',
            'variants.*.price.min' => 'Harga variasi minimal 0.',
            'variants.*.price.max' => 'Harga variasi maksimal 1 miliar.',
            'variants.*.discount_price.numeric' => 'Harga promo variasi harus angka.',
            'variants.*.discount_price.min' => 'Harga promo variasi minimal 0.',
            'variants.*.discount_price.max' => 'Harga promo variasi maksimal 1 miliar.',
            'variants.*.variant_code.string' => 'Kode variasi harus teks.',
            'variants.*.variant_code.max' => 'Kode variasi maksimal 50 karakter.',
            'variants.*.variant_code.distinct' => 'Kode variasi harus unik.',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $type = $this->input('distribution_type');
            $users = $this->input('responsible_users', []);
            $values = $this->input('distribution_values', []);

            // Validasi kesesuaian antara selected users dan distribution values
            if ($type !== 'equal' && count($users) !== count($values)) {
                $validator->errors()->add(
                    'distribution_values',
                    'Jumlah nilai distribusi harus sama dengan jumlah penanggung jawab.'
                );
            }

            // Pastikan semua user yang dipilih memiliki nilai distribusi
            if ($type !== 'equal') {
                foreach ($users as $userId) {
                    if (!array_key_exists($userId, $values)) {
                        $validator->errors()->add(
                            'distribution_values',
                            "User ID {$userId} tidak memiliki nilai distribusi."
                        );
                        break;
                    }
                }
            }
        });
    }
}
