<?php

namespace App\Services\MonitoringCpr;

use App\Repositories\MonitoringCpr\DataMonitoringCprRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class DataMonitoringCprService
{
    private $previousValues = [];
    private $consecutiveSkips = 0; // Untuk menghitung jumlah skip berturut-turut
    public function __construct(protected DataMonitoringCprRepository $DataMonitoringCprRepository)
    {
    }

    /**
     * Store data MonitoringCpr
     * @param array $data
     * @return mixed
     */
    public function updateOrCreate(array $data)
    {
        return $this->DataMonitoringCprRepository->updateOrCreate($data);
    }
    public function getDataIdUnique($dateRange, $idAkun, $status = null)
    {
        return $this->DataMonitoringCprRepository->getDataIdUnique($dateRange, $idAkun, $status);
    }

    /**
     * Get statistics for dashboard
     */
    public function getStatistics($dateRange, $idAkun, $status = null)
    {
        return $this->DataMonitoringCprRepository->getStatistics($dateRange, $idAkun, $status);
    }
}
