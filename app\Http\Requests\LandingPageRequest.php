<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LandingPageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'product_id' => 'required|exists:products,id',
            'main_title' => 'required|string|max:255',
            'facebook_pixel_id' => 'nullable|exists:facebook_pixels,id',
            'pixel_events' => 'nullable|array',
            'pixel_events.*' => 'nullable|string|in:PageView,ViewContent,Search,AddToCart,AddToWishlist,InitiateCheckout,AddPaymentInfo,Purchase,Lead,CompleteRegistration,Contact,CustomizeProduct,Donate,FindLocation,Schedule,StartTrial,SubmitApplication,Subscribe',
            'pixel_event_parameters' => 'nullable|array',
            'pixel_event_parameters.*' => 'nullable',
            
            // Optional section validation
            'section3_enabled' => 'nullable|boolean',
            'section4_enabled' => 'nullable|boolean',
            'section5_enabled' => 'nullable|boolean',
            
            // Section content validation
            'sub_title' => 'nullable|string|max:500',
            'content_description' => 'nullable|string',
            'background_color' => 'nullable|string|max:7',
            
            // Section 2 validation
            'section2_product_id' => 'nullable|exists:products,id',
            'section2_title' => 'nullable|string|max:255',
            'section2_sub_title' => 'nullable|string|max:500',
            'section2_content' => 'nullable|string',
            'section2_background_color' => 'nullable|string|max:7',
            'section2_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Section 3 validation (conditional)
            'section3_product_id' => 'nullable|exists:products,id',
            'section3_title' => 'nullable|string|max:255',
            'section3_sub_title' => 'nullable|string|max:500',
            'section3_content' => 'nullable|string',
            'section3_background_color' => 'nullable|string|max:7',
            'section3_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Section 4 validation (conditional)
            'section4_product_id' => 'nullable|exists:products,id',
            'section4_title' => 'nullable|string|max:255',
            'section4_sub_title' => 'nullable|string|max:500',
            'section4_content' => 'nullable|string',
            'section4_background_color' => 'nullable|string|max:7',
            'section4_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Section 5 validation (conditional)
            'section5_product_id' => 'nullable|exists:products,id',
            'section5_title' => 'nullable|string|max:255',
            'section5_sub_title' => 'nullable|string|max:500',
            'section5_content' => 'nullable|string',
            'section5_background_color' => 'nullable|string|max:7',
            'section5_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Section 6 validation
            'section6_product_id' => 'nullable|exists:products,id',
            'section6_background_color' => 'nullable|string|max:7',
            'section6_customer_name_label' => 'nullable|string|max:100',
            'section6_customer_name_placeholder' => 'nullable|string|max:100',
            'section6_whatsapp_label' => 'nullable|string|max:100',
            'section6_whatsapp_placeholder' => 'nullable|string|max:100',
            'section6_address_label' => 'nullable|string|max:100',
            'section6_address_placeholder' => 'nullable|string|max:100',
            'section6_city_label' => 'nullable|string|max:100',
            'section6_city_default' => 'nullable|string|max:100',
            'section6_notes_label' => 'nullable|string|max:100',
            'section6_notes_placeholder' => 'nullable|string|max:100',
            'section6_shipping_label' => 'nullable|string|max:100',
            'section6_shipping_service_label' => 'nullable|string|max:100',
            'section6_payment_label' => 'nullable|string|max:100',
            'section6_enable_bank_transfer' => 'nullable|boolean',
            'section6_enable_cod' => 'nullable|boolean',
            'section6_product_price' => 'nullable|numeric|min:0',
            'section6_shipping_cost' => 'nullable|numeric|min:0',
            'section6_total_price' => 'nullable|numeric|min:0',
            'section6_order_button_text' => 'nullable|string|max:50',
            'section6_section1_button_text' => 'nullable|string|max:50',
        ];

        // Add image validation based on request method
        if ($this->isMethod('post')) {
            $rules['section_image'] = 'required|image|mimes:jpeg,png,jpg,gif|max:2048';
        } else {
            $rules['section_image'] = 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048';
        }

        return $rules;
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama Landing Page harus diisi',
            'product_id.required' => 'Produk harus dipilih',
            'product_id.exists' => 'Produk yang dipilih tidak valid',
            'main_title.required' => 'Judul Landing Page harus diisi',
            'section_image.required' => 'Gambar Section 1 harus diupload',
            'section_image.image' => 'File harus berupa gambar',
            'section_image.mimes' => 'Format gambar harus JPG, PNG, atau GIF',
            'section_image.max' => 'Ukuran gambar maksimal 2MB',
            'facebook_pixel_id.exists' => 'Facebook Pixel yang dipilih tidak valid',
            'pixel_events.array' => 'Format events tidak valid',
            'pixel_events.*.in' => 'Event harus salah satu dari event Facebook Pixel yang valid',
            'pixel_event_parameters.array' => 'Format parameters tidak valid',
            
            // Image validation messages
            '*.image' => 'File harus berupa gambar',
            '*.mimes' => 'Format gambar harus JPG, PNG, atau GIF',
            '*.max' => 'Ukuran gambar maksimal 2MB',
            
            // Product validation messages
            'section2_product_id.exists' => 'Produk Section 2 yang dipilih tidak valid',
            'section3_product_id.exists' => 'Produk Section 3 yang dipilih tidak valid',
            'section4_product_id.exists' => 'Produk Section 4 yang dipilih tidak valid',
            'section5_product_id.exists' => 'Produk Section 5 yang dipilih tidak valid',
            'section6_product_id.exists' => 'Produk Section 6 yang dipilih tidak valid',
            
            // Numeric validation messages
            'section6_product_price.numeric' => 'Harga produk harus berupa angka',
            'section6_product_price.min' => 'Harga produk tidak boleh negatif',
            'section6_shipping_cost.numeric' => 'Biaya pengiriman harus berupa angka',
            'section6_shipping_cost.min' => 'Biaya pengiriman tidak boleh negatif',
            'section6_total_price.numeric' => 'Total harga harus berupa angka',
            'section6_total_price.min' => 'Total harga tidak boleh negatif',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama Landing Page',
            'product_id' => 'Produk',
            'main_title' => 'Judul Utama',
            'section_image' => 'Gambar Section 1',
            'facebook_pixel_id' => 'Facebook Pixel',
            'pixel_events' => 'Events Pixel',
            'pixel_event_parameters' => 'Parameter Events',
            
            'section2_product_id' => 'Produk Section 2',
            'section2_title' => 'Judul Section 2',
            'section2_sub_title' => 'Sub Judul Section 2',
            'section2_content' => 'Konten Section 2',
            'section2_image' => 'Gambar Section 2',
            
            'section3_product_id' => 'Produk Section 3',
            'section3_title' => 'Judul Section 3',
            'section3_sub_title' => 'Sub Judul Section 3',
            'section3_content' => 'Konten Section 3',
            'section3_image' => 'Gambar Section 3',
            
            'section4_product_id' => 'Produk Section 4',
            'section4_title' => 'Judul Section 4',
            'section4_sub_title' => 'Sub Judul Section 4',
            'section4_content' => 'Konten Section 4',
            'section4_image' => 'Gambar Section 4',
            
            'section5_product_id' => 'Produk Section 5',
            'section5_title' => 'Judul Section 5',
            'section5_sub_title' => 'Sub Judul Section 5',
            'section5_content' => 'Konten Section 5',
            'section5_image' => 'Gambar Section 5',
            
            'section6_product_id' => 'Produk Section 6',
            'section6_product_price' => 'Harga Produk',
            'section6_shipping_cost' => 'Biaya Pengiriman',
            'section6_total_price' => 'Total Harga',
            'section6_order_button_text' => 'Teks Tombol Order',
            'section6_section1_button_text' => 'Teks Tombol Section 1',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation for enabled sections
            if ($this->boolean('section3_enabled')) {
                if (!$this->filled('section3_title')) {
                    $validator->errors()->add('section3_title', 'Judul Section 3 harus diisi jika section diaktifkan');
                }
            }
            
            if ($this->boolean('section4_enabled')) {
                if (!$this->filled('section4_title')) {
                    $validator->errors()->add('section4_title', 'Judul Section 4 harus diisi jika section diaktifkan');
                }
            }
            
            if ($this->boolean('section5_enabled')) {
                if (!$this->filled('section5_title')) {
                    $validator->errors()->add('section5_title', 'Judul Section 5 harus diisi jika section diaktifkan');
                }
            }
        });
    }
}
