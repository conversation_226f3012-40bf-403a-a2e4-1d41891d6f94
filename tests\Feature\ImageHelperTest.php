<?php

namespace Tests\Feature;

use App\Helpers\ImageHelper;
use Tests\TestCase;

class ImageHelperTest extends TestCase
{
    public function test_handles_external_urls()
    {
        $url = 'https://example.com/image.jpg';
        $result = ImageHelper::getImageUrl($url);
        
        $this->assertEquals($url, $result);
    }

    public function test_handles_storage_prefixed_paths()
    {
        $path = 'storage/landing-pages/image.jpg';
        $result = ImageHelper::getImageUrl($path);
        
        // Should return asset() result
        $this->assertStringContains('storage/landing-pages/image.jpg', $result);
    }

    public function test_handles_relative_paths()
    {
        $path = 'landing-pages/image.jpg';
        $result = ImageHelper::getImageUrl($path);
        
        // Should prepend storage/ and use asset()
        $this->assertStringContains('storage/landing-pages/image.jpg', $result);
    }

    public function test_handles_empty_path()
    {
        $result = ImageHelper::getImageUrl(null);
        $this->assertEquals('', $result);
        
        $result = ImageHelper::getImageUrl('');
        $this->assertEquals('', $result);
    }

    public function test_handles_empty_path_with_fallback()
    {
        $fallback = 'https://example.com/default.jpg';
        $result = ImageHelper::getImageUrl(null, $fallback);
        $this->assertEquals($fallback, $result);
    }

    public function test_handles_products_path()
    {
        $path = 'products/product_123.jpg';
        $result = ImageHelper::getImageUrl($path);
        
        $this->assertStringContains('storage/products/product_123.jpg', $result);
    }

    public function test_handles_uploads_path()
    {
        $path = 'uploads/bukti_pembayaran/payment_123.jpg';
        $result = ImageHelper::getImageUrl($path);
        
        $this->assertStringContains('storage/uploads/bukti_pembayaran/payment_123.jpg', $result);
    }

    public function test_blade_directive_works()
    {
        // Test that the Blade directive is registered
        $this->assertTrue(app('blade.compiler')->getCustomDirectives()['imageUrl'] !== null);
    }
}
