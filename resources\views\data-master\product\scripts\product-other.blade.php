<script>
    $(document).ready(function() {
        const $distributionType = $('#distribution_type');
        const $responsibleUsers = $('#responsible_users');
        const $valuesContainer = $('#distribution_values_container');
        const $valuesInputs = $('#distribution_values_inputs');
        const $errorsContainer = $('#distribution_errors');

        // Initialize select2 if you're using it
        $responsibleUsers.select2({
            placeholder: "Pilih penanggung jawab",
            width: '100%'
        });

        // Function to get existing distribution value for a user
        function getExistingDistributionValue(userId, distributionType) {
            const $option = $responsibleUsers.find(`option[value="${userId}"]`);
            const existingType = $option.data('distribution-type');
            const existingValue = $option.data('distribution-value');

            // Only use existing value if the distribution type matches
            if (existingType === distributionType && existingValue) {
                return existingValue;
            }

            // Return default value based on type
            const selectedCount = $responsibleUsers.find('option:selected').length;
            return distributionType === 'percentage' ?
                (100 / selectedCount).toFixed(2) : '1';
        }

        // Function to update UI based on distribution type
        function updateDistributionUI() {
            const type = $distributionType.val();
            const selectedOptions = $responsibleUsers.find('option:selected');

            // Clear previous inputs and errors
            $valuesInputs.empty();
            $errorsContainer.empty();

            if (type === 'equal' || selectedOptions.length === 0) {
                $valuesContainer.hide();
                return;
            }

            $valuesContainer.show();
            let totalPercentage = 0;

            selectedOptions.each(function() {
                const $option = $(this);
                const userId = $option.val();
                const userName = $option.text().split(' - ')[0];

                // Get the appropriate distribution value
                const currentValue = getExistingDistributionValue(userId, type);

                const inputGroup = $(`
                    <div class="mb-3">
                        <label class="form-label">${userName}</label>
                        <div class="input-group">
                            <input type="number"
                                class="form-control distribution-value"
                                name="distribution_values[${userId}]"
                                value="${currentValue}"
                                min="${type === 'percentage' ? '0.01' : '1'}"
                                max="${type === 'percentage' ? '100' : ''}"
                                step="${type === 'percentage' ? '0.01' : '1'}"
                                required
                                data-user-id="${userId}">
                            <span class="input-group-text">${type === 'percentage' ? '%' : 'order'}</span>
                        </div>
                        <div class="form-text text-muted">
                            Nilai distribusi untuk ${userName}
                        </div>
                    </div>
                `);

                $valuesInputs.append(inputGroup);

                if (type === 'percentage') {
                    totalPercentage += parseFloat(currentValue) || 0;
                }
            });

            // For percentage type, show total percentage
            if (type === 'percentage') {
                const isValid = Math.abs(totalPercentage - 100) < 0.01;
                const $totalDiv = $(`
                    <div class="alert ${isValid ? 'alert-success' : 'alert-warning'}">
                        <div class="d-flex align-items-center">
                            <i class="bx ${isValid ? 'bx-check-circle' : 'bx-info-circle'} me-2"></i>
                            <div>
                                <strong>Total Persentase: ${totalPercentage.toFixed(2)}%</strong>
                                ${isValid ?
                                    '<div class="small text-success">✓ Total sudah tepat 100%</div>' :
                                    '<div class="small">⚠ Total harus tepat 100%</div>'
                                }
                            </div>
                        </div>
                    </div>
                `);
                $valuesInputs.append($totalDiv);
            }
        }

        // Function to validate distribution values before form submission
        function validateDistribution() {
            const type = $distributionType.val();
            $errorsContainer.empty();

            if (type === 'percentage') {
                let total = 0;
                let isValid = true;

                $('.distribution-value').each(function() {
                    const value = parseFloat($(this).val()) || 0;
                    total += value;

                    if (value <= 0) {
                        isValid = false;
                        const userId = $(this).data('user-id');
                        const userName = $responsibleUsers.find(`option[value="${userId}"]`).text().split(' - ')[0];
                        $errorsContainer.append(`<div>Nilai untuk ${userName} harus lebih dari 0%</div>`);
                    }
                });

                if (Math.abs(total - 100) > 0.01) {
                    isValid = false;
                    $errorsContainer.append(`<div>Total persentase harus tepat 100%. Saat ini: ${total.toFixed(2)}%</div>`);
                }

                return isValid;
            }

            return true;
        }

        // Function to check if we should show distribution values on page load
        function shouldShowDistributionOnLoad() {
            const type = $distributionType.val();
            const selectedOptions = $responsibleUsers.find('option:selected');

            // Show if not equal type and has selected users
            if (type !== 'equal' && selectedOptions.length > 0) {
                return true;
            }

            // Also show if any selected user has existing distribution data
            let hasExistingData = false;
            selectedOptions.each(function() {
                const $option = $(this);
                const existingType = $option.data('distribution-type');
                const existingValue = $option.data('distribution-value');
                if (existingType && existingValue) {
                    hasExistingData = true;
                    return false; // break the loop
                }
            });

            return hasExistingData;
        }

        // Event listeners
        $distributionType.on('change', updateDistributionUI);
        $responsibleUsers.on('change', updateDistributionUI);

        // Realtime validation for percentage inputs with debounce to prevent loops
        let updateTimeout;
        $valuesInputs.on('input', '.distribution-value', function() {
            if ($distributionType.val() === 'percentage') {
                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(function() {
                    updateDistributionUI();
                }, 300); // Debounce for 300ms
            }
        });

        // Enhanced form submission validation - attach to window for global access
        window.validateResponsibleUsersDistribution = function() {
            const type = $distributionType.val();
            const selectedUsers = $responsibleUsers.val() || [];
            const distributionValues = {};

            // Collect distribution values
            $('.distribution-value').each(function() {
                const userId = $(this).data('user-id');
                const value = parseFloat($(this).val()) || 0;
                distributionValues[userId] = value;
            });

            // Debug logging
            console.log('Form validation data:', {
                distribution_type: type,
                responsible_users: selectedUsers,
                distribution_values: distributionValues
            });

            if (type === 'percentage' && !validateDistribution()) {
                $errorsContainer.append('<div class="mt-2 alert alert-danger">Silakan perbaiki kesalahan di atas sebelum menyimpan.</div>');
                $valuesContainer.get(0).scrollIntoView({
                    behavior: 'smooth'
                });
                return false;
            }

            // Additional validation for fixed type
            if (type === 'fixed') {
                let isValid = true;
                $('.distribution-value').each(function() {
                    const value = parseInt($(this).val()) || 0;
                    if (value < 1) {
                        isValid = false;
                        const userId = $(this).data('user-id');
                        const userName = $responsibleUsers.find(`option[value="${userId}"]`).text().split(' - ')[0];
                        $errorsContainer.append(`<div class="alert alert-danger">Nilai untuk ${userName} harus minimal 1 order</div>`);
                    }
                });

                if (!isValid) {
                    $valuesContainer.get(0).scrollIntoView({
                        behavior: 'smooth'
                    });
                    return false;
                }
            }

            return true;
        };

        // Initialize UI - improved to handle edit mode
        $(document).ready(function() {
            // Small delay to ensure select2 is fully initialized
            setTimeout(function() {
                if (shouldShowDistributionOnLoad()) {
                    updateDistributionUI();
                }
            }, 100);
        });

        // Also initialize immediately
        updateDistributionUI();
    });
</script>
