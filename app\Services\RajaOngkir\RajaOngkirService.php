<?php

namespace App\Services\RajaOngkir;

use App\Models\ApiKey;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class RajaOngkirService
{
    protected $client;
    protected $apiKey;
    protected $baseUrl;
    protected $config;

    public function __construct()
    {
        // Inisialisasi Guzzle HTTP Client
        $this->client = new Client();

        // Load configuration from database
        try {
            $this->loadConfigFromDatabase();
        } catch (\Exception $e) {
            // Service akan tetap bisa di-instantiate, tapi method isConfigured() akan return false
            Log::warning('RajaOngkir service initialized without proper configuration: ' . $e->getMessage());
        }
    }

    /**
     * Load RajaOngkir configuration from database
     */
    protected function loadConfigFromDatabase()
    {
        try {
            // Cache configuration for 1 hour
            $this->config = Cache::remember('rajaongkir_config', 3600, function () {
                return ApiKey::first();
            });

            if ($this->config && !empty($this->config->rajaongkir_api_key)) {
                $this->baseUrl = $this->config->rajaongkir_endpoint ?? 'https://pro.rajaongkir.com';
                $this->apiKey = $this->config->rajaongkir_api_key;
            } else {
                throw new \Exception('RajaOngkir API key is not configured in database');
            }
        } catch (\Exception $e) {
            Log::error('RajaOngkir configuration load error: ' . $e->getMessage());
            throw new \Exception('RajaOngkir configuration error: ' . $e->getMessage());
        }
    }

    /**
     * Check if RajaOngkir is configured
     */
    public function isConfigured()
    {
        return !empty($this->apiKey) && !empty($this->baseUrl);
    }

    /**
     * Test connection to RajaOngkir API
     */
    public function testConnection(): array
    {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'RajaOngkir API key is not configured',
            ];
        }

        try {
            $response = $this->client->get($this->baseUrl . '/starter/province', [
                'query' => ['id' => 12],
                'headers' => [
                    'key' => $this->apiKey
                ],
                'timeout' => 30, // seconds
                'http_errors' => false // Prevents Guzzle from throwing exceptions on 4xx/5xx
            ]);

            $statusCode = $response->getStatusCode();
            if ($statusCode !== 200) {
                return [
                    'success' => false,
                    'message' => 'HTTP Error: ' . $statusCode,
                ];
            }

            $body = json_decode($response->getBody()->getContents(), true);
            $status = $body['rajaongkir']['status'] ?? [];

            if (($status['code'] ?? null) === 200) {
                return [
                    'success' => true,
                    'message' => 'RajaOngkir connection successful',
                    'data' => [
                        'status' => $status['description'] ?? 'OK',
                        'provinces_count' => count($body['rajaongkir']['results'] ?? []),
                    ],
                ];
            }

            return [
                'success' => false,
                'message' => 'RajaOngkir API error: ' . ($status['description'] ?? 'Unknown error'),
            ];

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            Log::error('RajaOngkir connection error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Cannot connect to RajaOngkir server. Please check your internet connection.',
            ];

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();

            $errorMessages = [
                401 => 'Invalid API key. Please check your RajaOngkir API key.',
                403 => 'Access forbidden. Your API key may not have sufficient permissions.',
            ];

            $message = $errorMessages[$statusCode] ?? 'API request failed: HTTP ' . $statusCode;

            Log::error("RajaOngkir client error ({$statusCode}): " . $e->getMessage());

            return [
                'success' => false,
                'message' => $message,
            ];

        } catch (\Throwable $e) {
            Log::error('RajaOngkir test connection error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
            ];
        }
    }


    /**
     * Simple test that just validates API key format and connectivity
     */
    public function basicConnectionTest()
    {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'RajaOngkir is not configured'
            ];
        }

        // Basic validation
        if (strlen($this->apiKey) < 10) {
            return [
                'success' => false,
                'message' => 'API key appears to be invalid (too short)'
            ];
        }

        try {
            // Simple HEAD request to check if server is reachable
            $response = $this->client->head($this->baseUrl, ['timeout' => 5]);

            if ($response->getStatusCode() < 500) {
                return [
                    'success' => true,
                    'message' => 'Basic connectivity test passed',
                    'data' => [
                        'endpoint' => $this->baseUrl,
                        'status_code' => $response->getStatusCode()
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Server error: HTTP ' . $response->getStatusCode()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Basic connectivity failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Melacak nomor resi menggunakan API RajaOngkir.
     *
     * @param string $waybill Nomor resi
     * @param string $courier Nama kurir
     * @return array Data hasil tracking
     * @throws \Exception Jika terjadi kesalahan
     */
    public function trackWaybill(string $waybill, string $courier): array
    {
        if (!$this->isConfigured()) {
            throw new \Exception('RajaOngkir API key is not configured');
        }

        try {
            $response = $this->client->post("$this->baseUrl/api/waybill", [
                'headers' => [
                    'content-type' => 'application/x-www-form-urlencoded',
                    'key' => $this->apiKey
                ],
                'form_params' => [
                    'waybill' => $waybill,
                    'courier' => $courier
                ],
                'timeout' => 30
            ]);

            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);

                // Check if API response is successful
                if (isset($data['rajaongkir']['status']['code']) && $data['rajaongkir']['status']['code'] == 200) {
                    return $data;
                } else {
                    throw new \Exception($data['rajaongkir']['status']['description'] ?? 'Unknown RajaOngkir API error');
                }
            }

            return [
                'meta' => [
                    'code' => $response->getStatusCode(),
                    'status' => 'error',
                    'message' => 'Failed to track RajaOngkir Shipping',
                ],
                'data' => []
            ];
        } catch (\Exception $e) {
            Log::error('RajaOngkir API Error (Track Waybill): ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get shipping cost calculation
     */
    public function getCost($origin, $destination, $weight, $courier)
    {
        if (!$this->isConfigured()) {
            throw new \Exception('RajaOngkir API key is not configured');
        }

        try {
            $response = $this->client->post("$this->baseUrl/starter/cost", [
                'headers' => [
                    'content-type' => 'application/x-www-form-urlencoded',
                    'key' => $this->apiKey
                ],
                'form_params' => [
                    'origin' => $origin,
                    'destination' => $destination,
                    'weight' => $weight,
                    'courier' => $courier
                ],
                'timeout' => 30
            ]);

            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);

                if (isset($data['rajaongkir']['status']['code']) && $data['rajaongkir']['status']['code'] == 200) {
                    return $data;
                } else {
                    throw new \Exception($data['rajaongkir']['status']['description'] ?? 'Unknown RajaOngkir API error');
                }
            }

            throw new \Exception('HTTP Error: ' . $response->getStatusCode());
        } catch (\Exception $e) {
            Log::error('RajaOngkir get cost error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Refresh configuration cache
     */
    public function refreshConfig()
    {
        Cache::forget('rajaongkir_config');
        $this->loadConfigFromDatabase();
    }
}
