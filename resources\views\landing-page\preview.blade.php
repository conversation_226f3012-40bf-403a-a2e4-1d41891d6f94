<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $landingPage->name }} - Preview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        .preview-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .section-preview {
            min-height: 400px;
            padding: 2rem 0;
            border-bottom: 2px dashed #dee2e6;
        }

        .section-title {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }

        .form-preview {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
    </style>
</head>

<body>
    <!-- Preview Header -->
    <div class="preview-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="bx bx-show me-2"></i>Preview: {{ $landingPage->name }}
                </h4>
                <div>
                    <span class="badge bg-light text-dark me-2">
                        <i class="bx bx-user me-1"></i>{{ $landingPage->visitors }} visitors
                    </span>
                    <a href="{{ route('landingPage.edit', $landingPage->id) }}" class="btn btn-light btn-sm">
                        <i class="bx bx-edit me-1"></i>Edit
                    </a>
                    <a href="{{ route('landingPage.index') }}" class="btn btn-outline-light btn-sm">
                        <i class="bx bx-arrow-back me-1"></i>Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 1 Preview -->
    @if ($landingPage->product_name || $landingPage->main_title)
        <div class="section-preview" style="background-color: {{ $landingPage->background_color ?? '#DCE8FD' }}">
            <div class="container">
                {{-- <div class="section-title">
                    <h5 class="mb-0"><i class="bx bx-layout me-2"></i>Section 1 - Main Content</h5>
                </div> --}}
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="display-4 fw-bold">{{ $landingPage->main_title ?? 'Judul Landing Page' }}</h1>
                        <h3 class="text-muted mb-4">{{ $landingPage->sub_title ?? 'Sub judul section' }}</h3>
                        @if ($landingPage->product)
                            <p class="lead">{{ $landingPage->product->name }}</p>
                        @endif
                        @if ($landingPage->content_description)
                            <div class="content-description">
                                {!! $landingPage->content_description !!}
                            </div>
                        @endif
                    </div>
                    <div class="col-md-6">
                        @if ($landingPage->section_image)
                            <img src="{{ asset('storage/' . $landingPage->section_image) }}" alt="Section Image"
                                class="img-fluid rounded shadow">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                style="height: 300px;">
                                <span class="text-muted"><i class="bx bx-image me-2"></i>No Image</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Section 2-5 Preview -->
    @for ($i = 2; $i <= 5; $i++)
        @php
            $sectionProduct = $landingPage->{"section{$i}Product"};
            $sectionData = [
                'product' => $sectionProduct,
                'background_color' => $landingPage->{"section{$i}_background_color"},
                'title' => $landingPage->{"section{$i}_title"},
                'sub_title' => $landingPage->{"section{$i}_sub_title"},
                'image' => $landingPage->{"section{$i}_image"},
                'content' => $landingPage->{"section{$i}_content"},
                'enable_section' => $landingPage->{"section{$i}_enable"},
            ];
        @endphp

        @if ($i == 2 || ($i > 2 && ($sectionData['enable_section'] ?? 0) == 1))
            @if (array_filter($sectionData))
                <div class="section-preview"
                    style="background-color: {{ $sectionData['background_color'] ?? '#DCE8FD' }}">
                    <div class="container">
                        {{-- <div class="section-title">
                            <h5 class="mb-0"><i class="bx bx-layout me-2"></i>Section {{ $i }}</h5>
                        </div> --}}
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                @if ($sectionData['title'])
                                    <h2 class="fw-bold">{{ $sectionData['title'] }}</h2>
                                @endif
                                @if ($sectionData['sub_title'])
                                    <h4 class="text-muted mb-3">{{ $sectionData['sub_title'] }}</h4>
                                @endif
                                @if ($sectionData['product'])
                                    <p class="lead">{{ $sectionData['product']->name }}</p>
                                @endif
                                @if ($sectionData['content'])
                                    <div class="content-description">
                                        {!! $sectionData['content'] !!}
                                    </div>
                                @endif
                            </div>
                            <div class="col-md-6">
                                @if ($sectionData['image'])
                                    <img src="{{ asset('storage/' . $sectionData['image']) }}"
                                        alt="Section {{ $i }} Image" class="img-fluid rounded shadow">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                        style="height: 250px;">
                                        <span class="text-muted"><i class="bx bx-image me-2"></i>No Image</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        @endif
    @endfor

    <!-- Section 6 Preview -->
    @if ($landingPage->section6)
        <div class="section-preview"
            style="background-color: {{ $landingPage->section6->background_color ?? '#DCE8FD' }}">
            <div class="container">
                {{-- <div class="section-title">
                    <h5 class="mb-0"><i class="bx bx-shopping-cart me-2"></i>Section 6 - Form Order/Checkout</h5>
                </div> --}}

                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="form-preview">
                            @if ($landingPage->section6->product)
                                <h3 class="text-center mb-4">{{ $landingPage->section6->product->name }}</h3>
                            @else
                                <h3 class="text-center mb-4">Nama Produk</h3>
                            @endif

                            <form>
                                <!-- Customer Information -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label
                                            class="form-label">{{ $landingPage->section6->customer_name_label }}</label>
                                        <input type="text" class="form-control"
                                            placeholder="{{ $landingPage->section6->customer_name_placeholder }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">{{ $landingPage->section6->whatsapp_label }}</label>
                                        <input type="text" class="form-control"
                                            placeholder="{{ $landingPage->section6->whatsapp_placeholder }}">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">{{ $landingPage->section6->address_label }}</label>
                                    <textarea class="form-control" rows="3" placeholder="{{ $landingPage->section6->address_placeholder }}"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">{{ $landingPage->section6->city_label }}</label>
                                        <select class="form-select">
                                            <option>{{ $landingPage->section6->city_default ?? 'Pilih Kota' }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">{{ $landingPage->section6->notes_label }}</label>
                                        <input type="text" class="form-control"
                                            placeholder="{{ $landingPage->section6->notes_placeholder }}">
                                    </div>
                                </div>

                                <!-- Shipping & Payment -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">{{ $landingPage->section6->shipping_label }}</label>
                                        <select class="form-select">
                                            <option>{{ $landingPage->section6->shipping_service_label }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">{{ $landingPage->section6->payment_label }}</label>
                                        <div class="d-flex gap-3">
                                            @if (($landingPage->section6->enable_bank_transfer ?? true) === true)
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="payment"
                                                        id="bank_transfer">
                                                    <label class="form-check-label" for="bank_transfer">
                                                        <i class="bx bx-credit-card me-1"></i>Bank Transfer
                                                    </label>
                                                </div>
                                            @endif
                                            @if (($landingPage->section6->enable_cod ?? false) === true)
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="payment"
                                                        id="cod">
                                                    <label class="form-check-label" for="cod">
                                                        <i class="bx bx-money me-1"></i>COD
                                                    </label>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Order Summary -->
                                <div class="bg-light rounded p-3 mb-4">
                                    <h6 class="mb-3"><i class="bx bx-receipt me-2"></i>Ringkasan Pesanan</h6>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Harga Produk:</span>
                                        <span>{{ $landingPage->section6->formatted_product_price }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Biaya Ongkir:</span>
                                        <span>{{ $landingPage->section6->formatted_shipping_cost }}</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total:</span>
                                        <span>{{ $landingPage->section6->formatted_total_price }}</span>
                                    </div>
                                </div>

                                <button type="button" class="btn btn-primary btn-lg w-100">
                                    {{ $landingPage->section6->order_button_text }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
