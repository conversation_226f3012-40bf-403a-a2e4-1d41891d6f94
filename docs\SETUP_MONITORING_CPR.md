# Setup Monitoring CPR - <PERSON>duan Lengkap

## 📋 Prerequisites

### System Requirements:
- PHP 8.1+
- <PERSON><PERSON> 11+
- MySQL 8.0+
- Redis (recommended)
- Facebook Ads API access

### Dependencies:
```bash
composer require facebook/php-business-sdk
composer require yajra/laravel-datatables-oracle
```

## 🚀 Installation Steps

### 1. Database Setup
```bash
# Run migrations
php artisan migrate

# Specifically for CPR monitoring
php artisan migrate --path=database/migrations/2025_04_14_023617_create_data_monitoring_cpr_table.php
php artisan migrate --path=database/migrations/2025_01_20_000001_add_indexes_to_data_monitoring_cpr_table.php
```

### 2. Queue Configuration
```bash
# Create jobs table
php artisan queue:table
php artisan migrate

# Start queue worker
php artisan queue:work --daemon --tries=3 --timeout=300
```

### 3. Facebook API Configuration
Update your `.env` file:
```env
# Facebook Ads API
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_ACCESS_TOKEN=your_access_token

# Queue Configuration
QUEUE_CONNECTION=database
```

### 4. Scheduler Setup
Add to your server's crontab:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## ⚙️ Configuration

### 1. Auto-Refresh Interval
Edit `resources/views/monitoring-cpr/index.blade.php`:
```javascript
// Change interval (in milliseconds)
}, 30000); // 30 seconds (default)
}, 60000); // 1 minute
}, 300000); // 5 minutes
```

### 2. Sync Schedule
Edit `app/Console/Kernel.php`:
```php
// Every 15 minutes during business hours
$schedule->command('cpr:sync --queue')
         ->everyFifteenMinutes()
         ->between('08:00', '20:00');

// Custom intervals:
->everyMinute()           // Every minute
->everyFiveMinutes()      // Every 5 minutes
->everyTenMinutes()       // Every 10 minutes
->everyThirtyMinutes()    // Every 30 minutes
->hourly()                // Every hour
```

### 3. CPR Color Coding
Edit the JavaScript in the view file:
```javascript
// Customize CPR thresholds
if (value > 50000) colorClass = 'text-danger';      // High CPR (red)
else if (value > 25000) colorClass = 'text-warning'; // Medium CPR (yellow)
else if (value > 0) colorClass = 'text-success';     // Low CPR (green)
```

## 🔧 Manual Commands

### Sync Commands:
```bash
# Sync all accounts
php artisan cpr:sync

# Sync specific account
php artisan cpr:sync --account-id=*********

# Sync specific user
php artisan cpr:sync --user-id=1

# Run in background queue
php artisan cpr:sync --queue

# Combination
php artisan cpr:sync --account-id=********* --queue
```

### Queue Management:
```bash
# Start queue worker
php artisan queue:work

# Restart queue workers
php artisan queue:restart

# Check failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Clear all jobs
php artisan queue:clear
```

## 🐛 Troubleshooting

### Common Issues:

#### 1. Facebook API Errors
```bash
# Check API configuration
php artisan tinker
>>> app(\App\Services\Facebook\FacebookAdService::class)->testConnection()
```

#### 2. Queue Not Processing
```bash
# Check queue status
php artisan queue:work --verbose

# Restart queue
php artisan queue:restart

# Check for failed jobs
php artisan queue:failed
```

#### 3. Database Performance
```bash
# Check indexes
SHOW INDEX FROM data_monitoring_cpr;

# Analyze slow queries
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
```

#### 4. Memory Issues
```bash
# Increase PHP memory limit
ini_set('memory_limit', '512M');

# Or in php.ini
memory_limit = 512M
```

### Log Files to Check:
- `storage/logs/laravel.log` - General errors
- `storage/logs/cpr-sync.log` - Sync specific logs
- `storage/logs/cpr-sync-daily.log` - Daily sync logs

## 📊 Monitoring & Maintenance

### Daily Checks:
1. **Queue Status**: Ensure workers are running
2. **Sync Logs**: Check for errors in sync process
3. **Database Size**: Monitor table growth
4. **API Limits**: Check Facebook API usage

### Weekly Maintenance:
1. **Log Cleanup**: Archive old logs
2. **Database Optimization**: Run OPTIMIZE TABLE
3. **Performance Review**: Check slow queries
4. **Backup**: Ensure data backup is working

### Monthly Review:
1. **Performance Metrics**: Analyze sync times
2. **Error Patterns**: Review recurring issues
3. **Capacity Planning**: Check resource usage
4. **Feature Usage**: Analyze user behavior

## 🔒 Security Considerations

### API Security:
- Store Facebook credentials securely
- Use environment variables
- Rotate access tokens regularly
- Monitor API usage

### Database Security:
- Use proper indexes
- Limit user permissions
- Regular security updates
- Monitor access logs

### Application Security:
- Validate all inputs
- Use CSRF protection
- Implement rate limiting
- Regular dependency updates

## 📈 Performance Optimization

### Database:
```sql
-- Optimize table
OPTIMIZE TABLE data_monitoring_cpr;

-- Analyze table
ANALYZE TABLE data_monitoring_cpr;

-- Check table status
SHOW TABLE STATUS LIKE 'data_monitoring_cpr';
```

### Caching:
```php
// Add to config/cache.php
'facebook_data' => [
    'driver' => 'redis',
    'ttl' => 900, // 15 minutes
],
```

### Queue Optimization:
```bash
# Multiple workers for high load
php artisan queue:work --queue=high,default --sleep=3 --tries=3
```

## 🎯 Best Practices

### Development:
1. **Test Locally**: Always test sync commands locally
2. **Use Queues**: Never run sync synchronously in production
3. **Error Handling**: Implement comprehensive error handling
4. **Logging**: Log all important operations

### Production:
1. **Monitor Resources**: CPU, memory, disk usage
2. **Backup Strategy**: Regular database backups
3. **Scaling**: Plan for increased data volume
4. **Documentation**: Keep setup documentation updated

### User Experience:
1. **Loading States**: Show loading indicators
2. **Error Messages**: Provide clear error messages
3. **Performance**: Optimize for fast loading
4. **Accessibility**: Ensure accessibility compliance
