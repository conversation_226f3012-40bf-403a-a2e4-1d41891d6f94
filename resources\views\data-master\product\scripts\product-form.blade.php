<script>
    Dropzone.autoDiscover = false;

    $(document).ready(function() {
        // Initialize tooltips
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });

        // const quill = new Quill('#description', {
        //     theme: 'snow'
        // });

        // const quill2 = new Quill('#advanced_description', {
        //     theme: 'snow'
        // });
        // // If we're editing a product, populate the Quill editor with the existing description
        // @if (isset($product) && $product->description)
        //     quill.root.innerHTML = {!! json_encode($product->description) !!};
        //     // Also set the hidden input value
        //     $('#description_hidden').val({!! json_encode($product->description) !!});
        // @endif

        // // If we're editing a product, populate the Quill editor with the existing description
        // @if (isset($product) && $product->advanced_description)
        //     quill2.root.innerHTML = {!! json_encode($product->advanced_description) !!};
        //     // Also set the hidden input value
        //     $('#advanced_description_hidden').val({!! json_encode($product->advanced_description) !!});
        // @endif

        const quill = new Quill('#description', {
            theme: 'snow'
        });

        const quill2 = new Quill('#advanced_description', {
            theme: 'snow'
        });

        // Populate editors if editing existing product
        @if (isset($product) && $product->description)
            quill.root.innerHTML = {!! json_encode($product->description) !!};
            document.getElementById('description_hidden').value = {!! json_encode($product->description) !!};
        @endif

        @if (isset($product) && $product->advanced_description)
            quill2.root.innerHTML = {!! json_encode($product->advanced_description) !!};
            document.getElementById('advanced_description_hidden').value = {!! json_encode($product->advanced_description) !!};
        @endif

        // Store editor references for later use
        window.productEditors = {
            description: quill,
            advanced_description: quill2
        };

        // Initialize select2 for category
        $('#category_id').select2({
            theme: "bootstrap-5",
            width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
            placeholder: "Pilih Kategori"
        });

        // Checkout URL generation from product name
        $('#name').on('input', function() {
            // Only auto-generate if checkout_url field is empty or hasn't been manually edited
            if (!$('#checkout_url').data('manually-edited')) {
                const productName = $(this).val();
                generateCheckoutUrl(productName);
            }

            // Update copy button even if URL wasn't auto-generated
            updateCopyUrlButton();
        });

        // Function to generate checkout URL from text
        function generateCheckoutUrl(text) {
            const generatedUrl = slugify(text, {
                lower: true,
                strict: true,
                trim: true
            });
            // const uniqueChar = Math.random().toString(36).substr(2, 5);
            // const uniqueUrl = generatedUrl + '-' + uniqueChar;
            const uniqueUrl = generatedUrl;
            $('#checkout_url').val(uniqueUrl);
            updateCopyUrlButton();
        }

        // Generate checkout URL button click handler
        $('#generate-url-btn').on('click', function() {
            const productName = $('#name').val();
            if (productName) {
                generateCheckoutUrl(productName);
                // Reset the manually edited flag
                $('#checkout_url').data('manually-edited', false);
            } else {
                // Show alert if product name is empty
                alert('Harap isi nama produk terlebih dahulu untuk menghasilkan URL.');
            }
        });

        // Track if the checkout URL has been manually edited
        $('#checkout_url').on('input', function() {
            $(this).data('manually-edited', true);
            updateCopyUrlButton();
        });

        // Update copy URL button text
        function updateCopyUrlButton() {
            const baseUrl = `${window.location.origin}/p/`;
            const slug = $('#checkout_url').val();
            const fullUrl = baseUrl + slug;

            // Update the copy button data attribute
            $('#copy-url-btn').attr('data-clipboard-text', fullUrl);
        }

        // Initialize copy button
        const clipboard = new ClipboardJS('#copy-url-btn');

        clipboard.on('success', function(e) {
            // Show copied message
            $('#url-copied').fadeIn();

            // Hide message after 2 seconds
            setTimeout(function() {
                $('#url-copied').fadeOut();
            }, 2000);

            e.clearSelection();
        });

        // Update copy URL button initially
        updateCopyUrlButton();

        // Price Type Tabs
        $('#simple-price-tab-btn').on('click', function() {
            $(this).addClass('active');
            $('#variant-price-tab-btn').removeClass('active');
            $('#simple-price-panel').addClass('show active');
            $('#variant-price-panel').removeClass('show active');
            $('#has_variants_hidden').val('0');

            // Show info about simple price validation
            $('#price-type-info').show();
            $('#price-type-info-text').html('Untuk harga simple, <strong>Harga Normal</strong> wajib diisi.');

            // Highlight the regular price field
            $('#regular_price').closest('.col-md-6').addClass('border-start border-primary border-3 ps-3');

            // Remove highlight from variant table if exists
            $('#variants-table').closest('.card').removeClass('border-start border-primary border-3 ps-3');
        });

        $('#variant-price-tab-btn').on('click', function() {
            $(this).addClass('active');
            $('#simple-price-tab-btn').removeClass('active');
            $('#variant-price-panel').addClass('show active');
            $('#simple-price-panel').removeClass('show active');
            $('#has_variants_hidden').val('1');

            // Update hidden inputs for variant attributes and values
            if (typeof updateHiddenInputs === 'function') {
                updateHiddenInputs();
            }

            // Show info about variant price validation
            $('#price-type-info').show();
            $('#price-type-info-text').html('Untuk harga variasi, <strong>minimal satu variasi</strong> harus dibuat dan <strong>semua harga variasi</strong> wajib diisi.');

            // Remove highlight from regular price field
            $('#regular_price').closest('.col-md-6').removeClass('border-start border-primary border-3 ps-3');

            // Highlight the variants table
            $('#variants-table').closest('.card').addClass('border-start border-primary border-3 ps-3');

            // Show warning when activating variants
            if ($('#variants-table tbody tr').length === 0) {
                Swal.fire({
                    title: "Informasi Variasi",
                    text: "Dengan mengaktifkan variasi, Anda perlu menentukan atribut produk (seperti warna, ukuran) dan membuat kombinasi harga. Harga produk utama akan menjadi harga default untuk semua variasi.",
                    icon: "info",
                    confirmButtonText: "Mengerti",
                });
            }
        });

        // Initialize price type tabs based on has_variants value
        if ($('#has_variants_hidden').val() === '1') {
            $('#variant-price-tab-btn').click();
        } else {
            // Show info about simple price validation on page load
            $('#price-type-info').show();
            $('#price-type-info-text').html('Untuk harga simple, <strong>Harga Normal</strong> wajib diisi.');

            // Highlight the regular price field
            $('#regular_price').closest('.col-md-6').addClass('border-start border-primary border-3 ps-3');
        }

        // Help attribute button
        $('#help-attribute-btn').on('click', function() {
            $('#attributeHelpModal').modal('show');
        });

        // Promo price toggle
        $('#has_promo').on('change', function() {
            if ($(this).is(':checked')) {
                $('#promo-price-container').slideDown();
                updateDiscountPreview();
            } else {
                $('#promo-price-container').slideUp();
                // Clear promo fields when toggled off
                $('#discount_price').val('');
                $('#discount-preview').hide();
            }
        });

        // Check if promo toggle should be initially checked
        if ($('#has_promo').is(':checked')) {
            $('#promo-price-container').show();
            updateDiscountPreview();
        }

        // Calculate and display discount percentage when prices change
        $('#regular_price, #discount_price').on('input', function() {
            updateDiscountPreview();
        });

        // Wholesale toggle is handled in product-wholesale.blade.php

        // Function to update discount preview
        function updateDiscountPreview() {
            const regularPrice = parseFloat($('#regular_price').val()) || 0;
            const salePrice = parseFloat($('#discount_price').val()) || 0;

            if (regularPrice > 0 && salePrice > 0 && salePrice < regularPrice) {
                const discountAmount = regularPrice - salePrice;
                const discountPercentage = Math.round((discountAmount / regularPrice) * 100);

                $('#discount-percentage').text(discountPercentage + '%');
                $('#discount-preview').show();
            } else {
                $('#discount-preview').hide();
            }
        }

        // Promotion period toggle
        $('#has_promo_period').on('change', function() {
            if ($(this).is(':checked')) {
                $('#promo-period-container').slideDown();
            } else {
                $('#promo-period-container').slideUp();
                // Clear date fields when toggled off
                $('#sale_start_date').val('');
                $('#sale_end_date').val('');
            }
        });

        // Check if promo period toggle should be initially checked
        if ($('#sale_start_date').val() || $('#sale_end_date').val() || $('#has_promo_period').is(':checked')) {
            $('#has_promo_period').prop('checked', true);
            $('#promo-period-container').show();
        }

        // Wholesale functionality is handled in product-wholesale.blade.php

        // Variants toggle
        $('#has_variants').on('change', function() {
            if ($(this).is(':checked')) {
                $('#variants-container').slideDown();
                $('#simple-price-container').slideUp();
                $('#variant-warning').slideDown();
            } else {
                $('#variants-container').slideUp();
                $('#simple-price-container').slideDown();
                $('#variant-warning').slideUp();
            }
            // Confirm deleting all variants
            if ($('#variants-table tbody tr').length > 0) {
                Swal.fire({
                    title: "Perhatian!",
                    text: "Menonaktifkan variasi akan menghapus semua variasi yang telah dibuat. Lanjutkan?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonText: "Ya, Hapus Semua",
                    cancelButtonText: "Batal",
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Clear all variants
                        $('#variants-table tbody').empty();
                        $('#attribute-cards').empty();
                        $('#empty-attributes-state').show();
                        $('#attribute-summary').hide();
                        $('#total-combinations').text('0');
                    } else {
                        // Re-check the toggle if user cancels
                        $('#has_variants').prop('checked', true);
                        $('#variants-container').slideDown();
                        $('#simple-price-container').slideUp();
                        $('#variant-warning').slideDown();
                    }
                });
            }

            // Check if variants toggle should be initially checked
            if ($('#has_variants').is(':checked')) {
                $('#variants-container').show();
                $('#simple-price-container').hide();
                $('#variant-warning').show();
            }
        });

        // Submit button handling - consolidated to prevent loops
        $('#submitBtn').on('click', function(e) {
            e.preventDefault();

            // Prevent multiple submissions
            if ($(this).prop('disabled')) {
                return false;
            }

            // Disable button immediately
            $(this).prop('disabled', true).html('<i class="bx bx-loader-alt bx-spin me-1"></i> Menyimpan...');

            // Process empty numeric fields if function exists
            if (typeof window.processEmptyNumericFields === 'function') {
                window.processEmptyNumericFields();
            }

            // Handle shipping address submission if function exists
            if (typeof window.handleShippingAddressSubmission === 'function') {
                window.handleShippingAddressSubmission();
            }

            // Handle shipping form submission if function exists
            if (typeof window.handleShippingFormSubmission === 'function') {
                window.handleShippingFormSubmission();
            }

            // Add checkout URL to validation if needed
            if ($('#checkout_url').val().trim() === '') {
                const productName = $('#name').val();
                if (productName) {
                    generateCheckoutUrl(productName);
                }
            }

            // Transfer Quill editor content to hidden input field
            document.getElementById('description_hidden').value = window.productEditors.description.root.innerHTML;
            document.getElementById('advanced_description_hidden').value = window.productEditors.advanced_description.root.innerHTML;

            // Update hidden inputs for variant attributes and values if has_variants is checked
            if ($('#has_variants_hidden').val() === '1' && typeof updateHiddenInputs === 'function') {
                updateHiddenInputs();
            }

            // Validate based on price type
            let isValid = true;
            let errorMessage = '';

            // Check if simple price is selected
            if ($('#has_variants_hidden').val() === '0') {
                // Validate regular price is filled
                if (!$('#regular_price').val() || $('#regular_price').val() <= 0) {
                    isValid = false;
                    errorMessage = 'Harga normal wajib diisi untuk produk dengan harga simple.';
                    $('#regular_price').addClass('is-invalid');
                } else {
                    $('#regular_price').removeClass('is-invalid');
                }
            } else {
                // Validate variant prices if variant price is selected
                if ($('#variants-table tbody tr').length === 0) {
                    isValid = false;
                    errorMessage = 'Anda harus menambahkan minimal satu variasi produk dengan harga.';
                } else {
                    // Check if all variant prices are filled
                    let emptyPriceFound = false;
                    $('#variants-table .variant-price').each(function() {
                        if (!$(this).val() || $(this).val() <= 0) {
                            emptyPriceFound = true;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });

                    if (emptyPriceFound) {
                        isValid = false;
                        errorMessage = 'Semua harga variasi wajib diisi.';
                    }
                }
            }

            // Validate responsible users distribution if function exists
            if (typeof window.validateResponsibleUsersDistribution === 'function') {
                if (!window.validateResponsibleUsersDistribution()) {
                    isValid = false;
                    errorMessage = 'Silakan perbaiki distribusi penanggung jawab.';
                }
            }

            if (!isValid) {
                // Re-enable button on validation failure
                $(this).prop('disabled', false).html('Simpan Produk');

                // Show error message
                Swal.fire({
                    title: "Validasi Gagal",
                    text: errorMessage,
                    icon: "error",
                    confirmButtonText: "OK"
                });
                return false;
            }

            // Prepare product images if function exists
            if (typeof window.prepareProductImages === 'function') {
                window.prepareProductImages();
            }

            // Enable all form elements before submission
            $('#productForm input, #productForm select, #productForm textarea').prop('disabled', false);

            // Submit the form directly without triggering other handlers
            document.getElementById('productForm').submit();
        });
    });

    // Wholesale functions are defined in product-wholesale.blade.php
</script>
