<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use App\Services\Facebook\FacebookAdService;
use App\Services\MonitoringCpr\DataMonitoringCprService;
use App\Models\User;

class SyncCprDataJob implements ShouldQueue
{
    use Queueable;

    protected $accountId;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct($accountId = null, $userId = null)
    {
        $this->accountId = $accountId;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(FacebookAdService $facebookAdService, DataMonitoringCprService $dataMonitoringCprService): void
    {
        try {
            Log::info('Starting CPR data sync job', [
                'account_id' => $this->accountId,
                'user_id' => $this->userId
            ]);

            // If specific account ID provided, sync only that account
            if ($this->accountId) {
                $this->syncAccountData($this->accountId, $facebookAdService, $dataMonitoringCprService);
            } else {
                // Sync all accounts for all users with Facebook Ads access
                $this->syncAllAccountsData($facebookAdService, $dataMonitoringCprService);
            }

            Log::info('CPR data sync job completed successfully');
        } catch (\Exception $e) {
            Log::error('CPR data sync job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Sync data for a specific account
     */
    private function syncAccountData($accountId, $facebookAdService, $dataMonitoringCprService)
    {
        try {
            $fullAccountId = 'act_' . $accountId;
            
            // Get campaigns
            $campaigns = $facebookAdService->getDataCampaigns($fullAccountId);
            if (isset($campaigns['error'])) {
                Log::warning('Failed to get campaigns', ['account_id' => $accountId, 'error' => $campaigns]);
                return;
            }

            // Get ad sets
            $adSets = $facebookAdService->getDataAdSet($fullAccountId);
            
            // Get insights
            $insights = $facebookAdService->getDataInsights($fullAccountId);

            // Index campaigns by id
            $indexedCampaigns = [];
            foreach ($campaigns as $campaign) {
                $indexedCampaigns[$campaign['id_kampanye']] = $campaign;
            }

            // Index ad sets by campaign id
            $indexedAdSets = [];
            foreach ($adSets as $adSet) {
                $indexedAdSets[$adSet['id_kampanye']][] = $adSet;
            }

            // Process insights and save data
            foreach ($insights as $insight) {
                $campaignId = $insight['id_kampanye'];
                $campaign = $indexedCampaigns[$campaignId] ?? null;
                $adSet = $indexedAdSets[$campaignId][0] ?? null; // Take first ad set for simplicity

                if (!$campaign) continue;

                $data = [
                    'nama_set_iklan' => $adSet['nama_set_iklan'] ?? $insight['nama_set_iklan'] ?? null,
                    'id_akun' => $accountId,
                    'id_kampanye' => $campaignId,
                    'anggaran_harian' => isset($adSet['anggaran_harian']) ? number_format($adSet['anggaran_harian'] / 100, 2, '.', '') : 0.00,
                    'spek_atribusi' => $adSet['spek_atribusi'] ?? null,
                    'status' => $campaign['status'] ?? $insight['status'] ?? null,
                    'tanggal_mulai' => isset($campaign['waktu_dibuat']) ? date('Y-m-d', strtotime($campaign['waktu_dibuat'])) : null,
                    'tanggal_berhenti' => isset($campaign['waktu_berhenti']) ? date('Y-m-d', strtotime($campaign['waktu_berhenti'])) : null,
                    'nama_kampanye' => $campaign['nama_kampanye'] ?? $insight['nama_kampanye'] ?? null,
                    'adlabels' => $campaign['adlabels'] ?? null,
                    'strategi_penawaran' => $campaign['strategi_bid'] ?? null,
                    'jangkauan' => $insight['jangkauan'] ?? 0,
                    'impresi' => $insight['impresi'] ?? 0,
                    'biaya_dibelanjakan' => $insight['biaya_dibelanjakan'] ?? 0,
                    'biaya_per_klik' => $insight['biaya_per_klik'] ?? 0,
                    'biaya_perhasil' => $insight['biaya_per_hasil'] ?? 0,
                    'hasil' => $insight['hasil'] ?? 0,
                ];

                $dataMonitoringCprService->updateOrCreate($data);
            }

            Log::info('Successfully synced data for account', ['account_id' => $accountId]);
        } catch (\Exception $e) {
            Log::error('Failed to sync account data', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Sync data for all accounts
     */
    private function syncAllAccountsData($facebookAdService, $dataMonitoringCprService)
    {
        // Get all users with Facebook Ads access
        $users = User::whereNotNull('fbads_id')->get();

        foreach ($users as $user) {
            if (empty($user->fbads_id)) continue;

            $accountIds = array_map('trim', explode(',', $user->fbads_id));
            
            foreach ($accountIds as $accountId) {
                if (empty($accountId)) continue;
                
                try {
                    $this->syncAccountData($accountId, $facebookAdService, $dataMonitoringCprService);
                } catch (\Exception $e) {
                    Log::warning('Failed to sync account in batch', [
                        'account_id' => $accountId,
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                    // Continue with next account instead of failing the entire job
                    continue;
                }
            }
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('SyncCprDataJob failed', [
            'account_id' => $this->accountId,
            'user_id' => $this->userId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
