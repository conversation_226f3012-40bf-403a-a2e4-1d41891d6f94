<?php

namespace App\Services\KiriminAja;

use App\Models\ApiKey;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class KiriminAjaBaseService
{
    protected $client;
    protected $baseUrl;
    protected $apiKey;
    protected $config;
    protected $environment;

    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $this->client = new Client();

        // Load configuration from database
        try {
            $this->loadConfigFromDatabase();
        } catch (\Exception $e) {
            // Service akan tetap bisa di-instantiate, tapi method isConfigured() akan return false
            Log::warning('KiriminAja service initialized without proper configuration: ' . $e->getMessage());
        }
    }

    /**
     * Load KiriminAja configuration from database
     */
    protected function loadConfigFromDatabase()
    {
        try {
            // Cache configuration for 1 hour
            $this->config = Cache::remember('kiriminaja_config', 3600, function () {
                return ApiKey::first();
            });

            if ($this->config && !empty($this->config->kiriminaja_env)) {
                $this->environment = $this->config->kiriminaja_env;

                if ($this->environment === 'prod') {
                    $this->baseUrl = $this->config->kiriminaja_prod_base_url ?? 'https://api.kiriminaja.com';
                    $this->apiKey = $this->config->kiriminaja_prod_api_key;
                } else {
                    $this->baseUrl = $this->config->kiriminaja_dev_base_url ?? 'https://tdev.kiriminaja.com';
                    $this->apiKey = $this->config->kiriminaja_dev_api_key;
                }

                if (empty($this->apiKey)) {
                    throw new \Exception("KiriminAja {$this->environment} API key is not configured in database");
                }
            } else {
                throw new \Exception('KiriminAja configuration is not found in database');
            }
        } catch (\Exception $e) {
            Log::error('KiriminAja configuration load error: ' . $e->getMessage());
            throw new \Exception('KiriminAja configuration error: ' . $e->getMessage());
        }
    }

    /**
     * Check if KiriminAja is configured
     */
    public function isConfigured()
    {
        return !empty($this->apiKey) && !empty($this->baseUrl) && !empty($this->environment);
    }

    /**
     * Test connection to KiriminAja API
     */
    public function testConnection()
    {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'KiriminAja API key is not configured'
            ];
        }

        try {
            // First try basic connectivity test
            $response = $this->client->head($this->baseUrl, [
                'timeout' => 5,
                'connect_timeout' => 3
            ]);

            if ($response->getStatusCode() >= 500) {
                return [
                    'success' => false,
                    'message' => 'KiriminAja server error: HTTP ' . $response->getStatusCode()
                ];
            }

            // Basic connectivity OK, now try with authentication
            return $this->testAuthentication();

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            return [
                'success' => false,
                'message' => 'Cannot connect to KiriminAja server. Please check your internet connection.'
            ];
        } catch (\Exception $e) {
            Log::error('KiriminAja test connection error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test authentication with simple endpoint
     */
    private function testAuthentication()
    {
        try {
            // Try simple GET request with auth header
            $response = $this->client->get($this->baseUrl . '/areas/provinces', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Accept' => 'application/json',
                ],
                'timeout' => 10,
                'connect_timeout' => 5
            ]);

            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);

                return [
                    'success' => true,
                    'message' => 'KiriminAja connection successful (' . $this->environment . ')',
                    'data' => [
                        'environment' => $this->environment,
                        'base_url' => $this->baseUrl,
                        'status_code' => $response->getStatusCode(),
                        'response_type' => is_array($data) ? 'array' : gettype($data)
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Unexpected HTTP status: ' . $response->getStatusCode()
                ];
            }
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();

            if ($statusCode == 401) {
                return [
                    'success' => false,
                    'message' => 'Invalid API key. Please check your KiriminAja API key.'
                ];
            } elseif ($statusCode == 403) {
                return [
                    'success' => false,
                    'message' => 'Access forbidden. Your API key may not have sufficient permissions.'
                ];
            } elseif ($statusCode == 404) {
                return [
                    'success' => false,
                    'message' => 'API endpoint not found. The service may be temporarily unavailable.'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API error: HTTP ' . $statusCode
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Authentication test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Make HTTP request to KiriminAja API
     */
    public function request($method, $endpoint, $params = [], $isJson = true)
    {
        if (!$this->isConfigured()) {
            return [
                'error' => true,
                'message' => 'KiriminAja API key is not configured'
            ];
        }

        try {
            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Accept' => 'application/json',
                ],
                'timeout' => 10,
                'connect_timeout' => 5
            ];

            $paramsOptions = [
                'GET' => ['query' => $params],
                'POST' => $isJson ? ['json' => $params] : ['form_params' => $params],
                'PUT' => $isJson ? ['json' => $params] : ['form_params' => $params],
                'DELETE' => $isJson ? ['json' => $params] : ['form_params' => $params]
            ];

            // Merge sesuai dengan Method yang digunakan
            $options = array_merge($options, $paramsOptions[$method] ?? []);

            $response = $this->client->request(strtolower($method), $this->baseUrl . $endpoint, $options);

            $data = json_decode($response->getBody()->getContents(), true);

            // Log successful requests untuk debugging
            Log::info('KiriminAja API request successful', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status' => $response->getStatusCode(),
                'environment' => $this->environment
            ]);

            return $data;
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            $errorMessage = 'Cannot connect to KiriminAja server. Please check your internet connection.';

            Log::error('KiriminAja connection error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'environment' => $this->environment,
                'base_url' => $this->baseUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'error' => true,
                'message' => $errorMessage
            ];
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            $errorMessage = "HTTP {$statusCode}";

            if ($statusCode == 401) {
                $errorMessage = 'Invalid API key. Please check your KiriminAja API key.';
            } elseif ($statusCode == 403) {
                $errorMessage = 'Access forbidden. Your API key may not have sufficient permissions.';
            } elseif ($statusCode == 404) {
                $errorMessage = 'API endpoint not found. Please check the endpoint URL.';
            } else {
                // Try to parse error message from response
                $errorData = json_decode($body, true);
                if (isset($errorData['message'])) {
                    $errorMessage = $errorData['message'];
                } else {
                    $errorMessage = "HTTP {$statusCode}: {$body}";
                }
            }

            Log::error('KiriminAja client error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'environment' => $this->environment,
                'status_code' => $statusCode,
                'response_body' => $body
            ]);

            return [
                'error' => true,
                'message' => $errorMessage
            ];
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $errorMessage = 'Request failed: ' . $e->getMessage();

            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $statusCode = $response->getStatusCode();
                $body = $response->getBody()->getContents();

                $errorMessage = "HTTP {$statusCode}: {$body}";
            }

            Log::error('KiriminAja request error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'environment' => $this->environment,
                'error' => $e->getMessage()
            ]);

            return [
                'error' => true,
                'message' => $errorMessage
            ];
        } catch (\Exception $e) {
            Log::error('KiriminAja unexpected error: ' . $e->getMessage());
            return [
                'error' => true,
                'message' => 'Unexpected error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get current environment
     */
    public function getEnvironment()
    {
        return $this->environment;
    }

    /**
     * Get base URL
     */
    public function getBaseUrl()
    {
        return $this->baseUrl;
    }

    /**
     * Refresh configuration cache
     */
    public function refreshConfig()
    {
        Cache::forget('kiriminaja_config');
        $this->loadConfigFromDatabase();
    }
}
