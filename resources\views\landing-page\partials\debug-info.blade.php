{{-- Debug Information for Landing Page Data --}}
@if (config('app.debug') && request()->has('debug'))
    <div class="debug-info" style="position: fixed; top: 10px; right: 10px; background: #fff; border: 2px solid #dc3545; padding: 15px; max-width: 400px; max-height: 80vh; overflow-y: auto; z-index: 9999; font-size: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <h5 style="color: #dc3545; margin-bottom: 10px;">🐛 Landing Page Debug Info</h5>
        
        <div class="debug-section">
            <strong>Basic Info:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                <li>ID: {{ $landingPage->id }}</li>
                <li>Name: {{ $landingPage->name }}</li>
                <li>Slug: {{ $landingPage->slug }}</li>
                <li>Active: {{ $landingPage->is_active ? 'Yes' : 'No' }}</li>
            </ul>
        </div>

        <div class="debug-section">
            <strong>Main Product:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                @if ($landingPage->product)
                    <li>✅ Product: {{ $landingPage->product->name }}</li>
                    <li>Price: Rp {{ number_format($landingPage->product->regular_price, 0, ',', '.') }}</li>
                @else
                    <li>❌ No main product</li>
                @endif
            </ul>
        </div>

        <div class="debug-section">
            <strong>Section 2:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                <li>Title: {{ $landingPage->section2_title ?: '❌ Empty' }}</li>
                <li>Content: {{ $landingPage->section2_content ? '✅ Has content' : '❌ Empty' }}</li>
                <li>Image: {{ $landingPage->section2_image ? '✅ Has image' : '❌ No image' }}</li>
                @if ($landingPage->section2Product)
                    <li>✅ Product: {{ $landingPage->section2Product->name }}</li>
                @else
                    <li>❌ No section2 product</li>
                @endif
            </ul>
        </div>

        <div class="debug-section">
            <strong>Section 3:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                <li>Enabled: {{ $landingPage->section3_enabled ? '✅ Yes' : '❌ No' }}</li>
                <li>Title: {{ $landingPage->section3_title ?: '❌ Empty' }}</li>
                <li>Content: {{ $landingPage->section3_content ? '✅ Has content' : '❌ Empty' }}</li>
                <li>Image: {{ $landingPage->section3_image ? '✅ Has image' : '❌ No image' }}</li>
                @if ($landingPage->section3Product)
                    <li>✅ Product: {{ $landingPage->section3Product->name }}</li>
                @else
                    <li>❌ No section3 product</li>
                @endif
            </ul>
        </div>

        <div class="debug-section">
            <strong>Section 4:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                <li>Enabled: {{ $landingPage->section4_enabled ? '✅ Yes' : '❌ No' }}</li>
                <li>Title: {{ $landingPage->section4_title ?: '❌ Empty' }}</li>
                <li>Content: {{ $landingPage->section4_content ? '✅ Has content' : '❌ Empty' }}</li>
                <li>Image: {{ $landingPage->section4_image ? '✅ Has image' : '❌ No image' }}</li>
                @if ($landingPage->section4Product)
                    <li>✅ Product: {{ $landingPage->section4Product->name }}</li>
                @else
                    <li>❌ No section4 product</li>
                @endif
            </ul>
        </div>

        <div class="debug-section">
            <strong>Section 5:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                <li>Enabled: {{ $landingPage->section5_enabled ? '✅ Yes' : '❌ No' }}</li>
                <li>Title: {{ $landingPage->section5_title ?: '❌ Empty' }}</li>
                <li>Content: {{ $landingPage->section5_content ? '✅ Has content' : '❌ Empty' }}</li>
                <li>Image: {{ $landingPage->section5_image ? '✅ Has image' : '❌ No image' }}</li>
                @if ($landingPage->section5Product)
                    <li>✅ Product: {{ $landingPage->section5Product->name }}</li>
                @else
                    <li>❌ No section5 product</li>
                @endif
            </ul>
        </div>

        <div class="debug-section">
            <strong>Section 6:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                @if ($landingPage->section6)
                    <li>✅ Section 6 exists</li>
                    <li>Product Price: {{ $landingPage->section6->product_price ?? 'Not set' }}</li>
                    <li>Shipping Cost: {{ $landingPage->section6->shipping_cost ?? 'Not set' }}</li>
                    <li>Total Price: {{ $landingPage->section6->total_price ?? 'Not set' }}</li>
                    <li>Bank Transfer: {{ $landingPage->section6->enable_bank_transfer ? '✅ Yes' : '❌ No' }}</li>
                    <li>COD: {{ $landingPage->section6->enable_cod ? '✅ Yes' : '❌ No' }}</li>
                    @if ($landingPage->section6->product)
                        <li>✅ Product: {{ $landingPage->section6->product->name }}</li>
                    @else
                        <li>❌ No section6 product</li>
                    @endif
                @else
                    <li>❌ Section 6 not configured</li>
                @endif
            </ul>
        </div>

        <div class="debug-section">
            <strong>Facebook Pixel:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
                @if ($landingPage->facebookPixel)
                    <li>✅ Pixel: {{ $landingPage->facebookPixel->name }}</li>
                    <li>Events: {{ is_array($landingPage->pixel_events) ? implode(', ', $landingPage->pixel_events) : 'None' }}</li>
                @else
                    <li>❌ No Facebook Pixel</li>
                @endif
            </ul>
        </div>

        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #ddd;">
            <small style="color: #666;">
                💡 Add <code>?debug=1</code> to URL to see this debug info<br>
                🔧 Only visible in debug mode
            </small>
        </div>
    </div>
@endif
