
import Echo from 'laravel-echo';

import Pusher from 'pusher-js';
window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY || '2rpzsjggxjhrnqvpyxso', // Fallback to 'default-key' if the environment variable is missing
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
    wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});
window.Echo.channel('orders')
.listen('.order.created', (e) => {
    console.log('New order created:', e);

    // Tambah notifikasi baru ke list
    // Ambil waktu order dibuat, fallback ke waktu sekarang jika tidak ada
    const createdAt = e.created_at ? new Date(e.created_at) : new Date();
    const now = new Date();
    const diffMs = now - createdAt;
    const diffMinutes = Math.floor(diffMs / 60000);

    let timeLabel = 'Baru';
    if (diffMinutes > 0) {
        timeLabel = `${diffMinutes} menit yang lalu`;
    }

    const notifItem = `
        <a class="dropdown-item" href="javascript:;">
            <div class="d-flex align-items-center">
                <div class="notify bg-light-primary text-primary"><i class="bx bx-group"></i></div>
                <div class="flex-grow-1">
                    <h6 class="msg-name">${e.judul ?? e.title}<span class="msg-time float-end">${timeLabel}</span></h6>
                    <p class="msg-info">${e.pesan ?? e.message}</p>
                </div>
            </div>
        </a>
        `;
    document.querySelector('.header-notifications-list').insertAdjacentHTML('afterbegin', notifItem);

    // Update badge jumlah notifikasi
    const alertCount = document.querySelector('.alert-count');
    if (alertCount) {
        let count = parseInt(alertCount.textContent, 10) || 0;
        alertCount.textContent = count + 1;
    } else {
        // Jika badge belum ada, buat baru
        const bellIcon = document.querySelector('.nav-link.dropdown-toggle');
        if (bellIcon) {
            const span = document.createElement('span');
            span.className = 'alert-count';
            span.textContent = '1';
            bellIcon.insertBefore(span, bellIcon.querySelector('i'));
        }
    }
});
