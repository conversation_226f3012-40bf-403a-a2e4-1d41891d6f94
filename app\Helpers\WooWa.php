<?php

namespace App\Helpers;

class WooWa
{
    public static function sendMessage($phone_no, $message, $url_params)
    {
        $key = 'adpfe27167c-53b6-49c7-8d33-8ca916a3e58a';
        $url = 'https://notifapi.com/' . $url_params;
        $data = [
            "phone_no" => $phone_no,
            "key"      => $key,
            "message"  => $message
        ];

        $data_string = json_encode($data, JSON_UNESCAPED_UNICODE);

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 0);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 360);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            // 'Content-Length: ' . strlen($data_string),
            // 'Authorization: Basic dXNtYW5ydWJpYW50b3JvcW9kcnFvZHJiZWV3b293YToyNjM3NmVkeXV3OWUwcmkzNDl1ZA=='
        ]);
        $res = curl_exec($ch);
        curl_close($ch);

        return $res;
    }
}
