<?php

namespace App\Repositories\Laporan;

use App\Models\DataMateriIklan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use PhpParser\Node\Expr\New_;

class PerformaIklanRepository
{
    public function __construct(protected DataMateriIklan $dataMateriIklanModel)
    {
    }

    /**
     * Get data order
     * @return mixed
     */
    public function grid($tanggal_awal, $tanggal_akhir)
    {
        $query = $this->dataMateriIklanModel
            ->whereBetween('created_at', [$tanggal_awal . ' 00:00:00', $tanggal_akhir . ' 23:59:59'])
            ->orderBy('created_at', 'desc') // Menampilkan dari tanggal terbaru
            ->get();

        return $query;
    }
}
