<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LandingPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'is_active' => $this->is_active,
            'visitors' => $this->visitors,
            'url' => $this->url,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Main content
            'main_title' => $this->main_title,
            'sub_title' => $this->sub_title,
            'background_color' => $this->background_color,
            'section_image' => $this->section_image ? asset('storage/' . $this->section_image) : null,
            'content_description' => $this->content_description,
            
            // Product information
            'product' => $this->whenLoaded('product', function () {
                return [
                    'id' => $this->product->id,
                    'name' => $this->product->name,
                    'regular_price' => $this->product->regular_price,
                    'discount_price' => $this->product->discount_price,
                    'current_price' => $this->product->current_price,
                    'has_discount' => $this->product->has_discount,
                ];
            }),
            
            // Facebook Pixel
            'facebook_pixel_id' => $this->facebook_pixel_id,
            'pixel_events' => $this->pixel_events,
            'pixel_event_parameters' => $this->pixel_event_parameters,
            
            // Section 2
            'section2' => [
                'product_id' => $this->section2_product_id,
                'background_color' => $this->section2_background_color,
                'title' => $this->section2_title,
                'sub_title' => $this->section2_sub_title,
                'content' => $this->section2_content,
                'image' => $this->section2_image ? asset('storage/' . $this->section2_image) : null,
                'product' => $this->whenLoaded('section2Product', function () {
                    return [
                        'id' => $this->section2Product->id,
                        'name' => $this->section2Product->name,
                        'regular_price' => $this->section2Product->regular_price,
                        'discount_price' => $this->section2Product->discount_price,
                    ];
                }),
            ],
            
            // Section 3
            'section3' => [
                'enabled' => $this->section3_enabled,
                'product_id' => $this->section3_product_id,
                'background_color' => $this->section3_background_color,
                'title' => $this->section3_title,
                'sub_title' => $this->section3_sub_title,
                'content' => $this->section3_content,
                'image' => $this->section3_image ? asset('storage/' . $this->section3_image) : null,
                'product' => $this->whenLoaded('section3Product', function () {
                    return [
                        'id' => $this->section3Product->id,
                        'name' => $this->section3Product->name,
                        'regular_price' => $this->section3Product->regular_price,
                        'discount_price' => $this->section3Product->discount_price,
                    ];
                }),
            ],
            
            // Section 4
            'section4' => [
                'enabled' => $this->section4_enabled,
                'product_id' => $this->section4_product_id,
                'background_color' => $this->section4_background_color,
                'title' => $this->section4_title,
                'sub_title' => $this->section4_sub_title,
                'content' => $this->section4_content,
                'image' => $this->section4_image ? asset('storage/' . $this->section4_image) : null,
                'product' => $this->whenLoaded('section4Product', function () {
                    return [
                        'id' => $this->section4Product->id,
                        'name' => $this->section4Product->name,
                        'regular_price' => $this->section4Product->regular_price,
                        'discount_price' => $this->section4Product->discount_price,
                    ];
                }),
            ],
            
            // Section 5
            'section5' => [
                'enabled' => $this->section5_enabled,
                'product_id' => $this->section5_product_id,
                'background_color' => $this->section5_background_color,
                'title' => $this->section5_title,
                'sub_title' => $this->section5_sub_title,
                'content' => $this->section5_content,
                'image' => $this->section5_image ? asset('storage/' . $this->section5_image) : null,
                'product' => $this->whenLoaded('section5Product', function () {
                    return [
                        'id' => $this->section5Product->id,
                        'name' => $this->section5Product->name,
                        'regular_price' => $this->section5Product->regular_price,
                        'discount_price' => $this->section5Product->discount_price,
                    ];
                }),
            ],
            
            // Section 6
            'section6' => $this->whenLoaded('section6', function () {
                return [
                    'id' => $this->section6->id,
                    'background_color' => $this->section6->background_color,
                    'customer_name_label' => $this->section6->customer_name_label,
                    'customer_name_placeholder' => $this->section6->customer_name_placeholder,
                    'whatsapp_label' => $this->section6->whatsapp_label,
                    'whatsapp_placeholder' => $this->section6->whatsapp_placeholder,
                    'address_label' => $this->section6->address_label,
                    'address_placeholder' => $this->section6->address_placeholder,
                    'city_label' => $this->section6->city_label,
                    'city_default' => $this->section6->city_default,
                    'notes_label' => $this->section6->notes_label,
                    'notes_placeholder' => $this->section6->notes_placeholder,
                    'shipping_label' => $this->section6->shipping_label,
                    'shipping_service_label' => $this->section6->shipping_service_label,
                    'payment_label' => $this->section6->payment_label,
                    'enable_bank_transfer' => $this->section6->enable_bank_transfer,
                    'enable_cod' => $this->section6->enable_cod,
                    'product_price' => $this->section6->product_price,
                    'shipping_cost' => $this->section6->shipping_cost,
                    'total_price' => $this->section6->total_price,
                    'order_button_text' => $this->section6->order_button_text,
                    'section1_button_text' => $this->section6->section1_button_text,
                    'formatted_product_price' => $this->section6->formatted_product_price,
                    'formatted_shipping_cost' => $this->section6->formatted_shipping_cost,
                    'formatted_total_price' => $this->section6->formatted_total_price,
                    'product' => $this->whenLoaded('section6.product', function () {
                        return [
                            'id' => $this->section6->product->id,
                            'name' => $this->section6->product->name,
                            'regular_price' => $this->section6->product->regular_price,
                            'discount_price' => $this->section6->product->discount_price,
                        ];
                    }),
                ];
            }),
            
            // User information
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                ];
            }),
        ];
    }
}
