<style>
    /* Ukuran font lebih kecil untuk tabel */
    #tabelDetailCampaign {
        font-size: 9px;
    }
    #tabelDetailCampaign thead th {
        font-size: 9px;
    }
    #tabelDetailCampaign tbody td {
        font-size: 9px;
    }
</style>
<div class="modal fade" id="modalCreateCampaign" tabindex="-1" aria-labelledby="modalCreateCampaignLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalCreateCampaignLabel"><?php echo e($namaAkun); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table id="tabelDetailCampaign" class="table datatable table-striped" style="width:100%">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Ka<PERSON>any<PERSON> (campaign)</th>                                
                                <th>Status</th>                                
                                <th>Hasil (result)</th>                                
                                <th>CPR (cost per result)</th>
                                <th>Jml Dibelanjakan (budget remaining)</th>                            
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>                
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        let campaign = <?php echo json_encode($campaign, 15, 512) ?>;

        // Tambahkan nomor urut manual untuk kolom DT_RowIndex
        campaign = campaign.map((item, index) => ({
            ...item,
            DT_RowIndex: index + 1
        }));

        let table = $('#tabelDetailCampaign').DataTable({
            data: campaign,
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },                       
                {
                    data: 'nama_kampanye',
                    render: data => data ? `<div style="text-align:left;">${data}</div>` : '-'
                },
                { data: 'status', render: data => data ?? '-' },
                { data: 'hasil', render: data => data ?? '-' },
                {
                    data: 'cpr',
                    render: data => {
                        if (!data) return '-';
                        let pesoValue = parseFloat(data);
                        let rupiah = pesoValue * 278.62;
                        return 'Rp ' + rupiah.toLocaleString('id-ID', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });
                    }
                },
                {
                    data: 'jumlah_dibelanjakan',
                    render: data => {
                        if (!data) return '-';
                        let pesoValue = parseFloat(data);
                        let rupiah = pesoValue * 278.62;
                        return 'Rp ' + rupiah.toLocaleString('id-ID', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });
                    }
                }
            ]
        });
    });
</script>



<?php /**PATH C:\laragon\www\jualinn\resources\views/monitoring-cpr/modal.blade.php ENDPATH**/ ?>