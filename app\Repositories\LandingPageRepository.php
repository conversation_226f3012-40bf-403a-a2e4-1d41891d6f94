<?php

namespace App\Repositories;

use App\Models\LandingPage;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class LandingPageRepository
{
    /**
     * Get landing pages for DataTables with optimized query
     */
    public function getForDataTable(int $userId)
    {
        return LandingPage::select([
            'id', 'name', 'slug', 'user_id', 'is_active', 'visitors', 'created_at'
        ])
        ->with([
            'product:id,name',
            'user:id,name'
        ])
        ->byUser($userId)
        ->active()
        ->latest();
    }

    /**
     * Get landing page with all relations for editing
     */
    public function getWithAllRelations(int $id): LandingPage
    {
        return LandingPage::with([
            'product:id,name,regular_price,discount_price',
            'section2Product:id,name,regular_price,discount_price',
            'section3Product:id,name,regular_price,discount_price',
            'section4Product:id,name,regular_price,discount_price',
            'section5Product:id,name,regular_price,discount_price',
            'section6.product:id,name,regular_price,discount_price'
        ])->findOrFail($id);
    }

    /**
     * Get landing page for public view with optimized relations
     */
    public function getForPublicView(string $slug): LandingPage
    {
        return LandingPage::with([
            'product:id,name,regular_price,discount_price',
            'section2Product:id,name,regular_price,discount_price',
            'section3Product:id,name,regular_price,discount_price',
            'section4Product:id,name,regular_price,discount_price',
            'section5Product:id,name,regular_price,discount_price',
            'section6.product:id,name,regular_price,discount_price'
        ])
        ->where('slug', $slug)
        ->active()
        ->firstOrFail();
    }

    /**
     * Get landing page for preview with all data
     */
    public function getForPreview(string $slug): LandingPage
    {
        return LandingPage::with([
            'product',
            'section2Product',
            'section3Product',
            'section4Product',
            'section5Product',
            'section6.product'
        ])->where('slug', $slug)->firstOrFail();
    }

    /**
     * Create new landing page
     */
    public function create(array $data): LandingPage
    {
        return LandingPage::create($data);
    }

    /**
     * Update landing page
     */
    public function update(LandingPage $landingPage, array $data): bool
    {
        return $landingPage->update($data);
    }

    /**
     * Delete landing page
     */
    public function delete(LandingPage $landingPage): bool
    {
        return $landingPage->delete();
    }

    /**
     * Find landing page by ID
     */
    public function findById(int $id): LandingPage
    {
        return LandingPage::findOrFail($id);
    }

    /**
     * Find landing page by slug
     */
    public function findBySlug(string $slug): ?LandingPage
    {
        return LandingPage::where('slug', $slug)->first();
    }

    /**
     * Get user's landing pages with pagination
     */
    public function getUserLandingPages(int $userId, int $perPage = 15): LengthAwarePaginator
    {
        return LandingPage::with(['product:id,name'])
            ->byUser($userId)
            ->active()
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get popular landing pages by visitor count
     */
    public function getPopularLandingPages(int $limit = 10): Collection
    {
        return LandingPage::with(['product:id,name', 'user:id,name'])
            ->active()
            ->orderBy('visitors', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent landing pages
     */
    public function getRecentLandingPages(int $limit = 10): Collection
    {
        return LandingPage::with(['product:id,name', 'user:id,name'])
            ->active()
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Search landing pages by name or product name
     */
    public function search(string $query, int $userId = null): Collection
    {
        $landingPages = LandingPage::with(['product:id,name'])
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('main_title', 'like', "%{$query}%")
                  ->orWhereHas('product', function ($productQuery) use ($query) {
                      $productQuery->where('name', 'like', "%{$query}%");
                  });
            })
            ->active();

        if ($userId) {
            $landingPages->byUser($userId);
        }

        return $landingPages->latest()->get();
    }

    /**
     * Get landing pages by product
     */
    public function getByProduct(int $productId): Collection
    {
        return LandingPage::with(['user:id,name'])
            ->where(function ($query) use ($productId) {
                $query->where('product_id', $productId)
                      ->orWhere('section2_product_id', $productId)
                      ->orWhere('section3_product_id', $productId)
                      ->orWhere('section4_product_id', $productId)
                      ->orWhere('section5_product_id', $productId)
                      ->orWhereHas('section6', function ($section6Query) use ($productId) {
                          $section6Query->where('product_id', $productId);
                      });
            })
            ->active()
            ->latest()
            ->get();
    }

    /**
     * Get landing page statistics for user
     */
    public function getUserStatistics(int $userId): array
    {
        $landingPages = LandingPage::byUser($userId)->active();
        
        return [
            'total_landing_pages' => $landingPages->count(),
            'total_visitors' => $landingPages->sum('visitors'),
            'average_visitors' => $landingPages->avg('visitors'),
            'most_visited' => $landingPages->orderBy('visitors', 'desc')->first(),
            'recent_count' => $landingPages->where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }

    /**
     * Increment visitor count
     */
    public function incrementVisitors(LandingPage $landingPage): void
    {
        $landingPage->increment('visitors');
    }

    /**
     * Toggle landing page status
     */
    public function toggleStatus(LandingPage $landingPage): bool
    {
        return $landingPage->update(['is_active' => !$landingPage->is_active]);
    }

    /**
     * Duplicate landing page
     */
    public function duplicate(LandingPage $landingPage, int $userId): LandingPage
    {
        $data = $landingPage->toArray();
        
        // Remove unique fields and set new values
        unset($data['id'], $data['created_at'], $data['updated_at']);
        $data['name'] = $data['name'] . ' (Copy)';
        $data['slug'] = $data['slug'] . '-copy-' . time();
        $data['user_id'] = $userId;
        $data['visitors'] = 0;
        
        $newLandingPage = $this->create($data);
        
        // Duplicate section 6 if exists
        if ($landingPage->section6) {
            $section6Data = $landingPage->section6->toArray();
            unset($section6Data['id'], $section6Data['created_at'], $section6Data['updated_at']);
            $section6Data['landing_page_id'] = $newLandingPage->id;
            $newLandingPage->section6()->create($section6Data);
        }
        
        return $newLandingPage;
    }

    /**
     * Get landing pages that need optimization (low visitor count)
     */
    public function getLowPerformingLandingPages(int $userId, int $visitorThreshold = 10): Collection
    {
        return LandingPage::with(['product:id,name'])
            ->byUser($userId)
            ->active()
            ->where('visitors', '<', $visitorThreshold)
            ->where('created_at', '<=', now()->subDays(7)) // At least 7 days old
            ->orderBy('visitors', 'asc')
            ->get();
    }
}
