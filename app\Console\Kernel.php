<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Auto-sync CPR data every 10 minutes during business hours (7 AM - 10 PM)
        $schedule->command('cpr:auto-sync --queue')
            ->everyTenMinutes()
            ->between('07:00', '22:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->appendOutputTo(storage_path('logs/cpr-auto-sync.log'));

        // Full sync twice daily (morning and evening)
        $schedule->command('cpr:auto-sync --force --queue')
            ->twiceDaily(8, 20) // 8 AM and 8 PM
            ->withoutOverlapping()
            ->runInBackground()
            ->appendOutputTo(storage_path('logs/cpr-full-sync.log'));

        // Legacy sync command (keep for manual use)
        // $schedule->command('cpr:sync --queue')
        //          ->dailyAt('02:00')
        //          ->withoutOverlapping()
        //          ->runInBackground()
        //          ->appendOutputTo(storage_path('logs/cpr-sync-daily.log'));

        // Clean up old logs weekly
        $schedule->call(function () {
            $logFiles = [
                storage_path('logs/cpr-auto-sync.log'),
                storage_path('logs/cpr-full-sync.log'),
                storage_path('logs/cpr-sync.log'),
                storage_path('logs/cpr-sync-daily.log')
            ];

            foreach ($logFiles as $logFile) {
                if (file_exists($logFile) && filesize($logFile) > 10 * 1024 * 1024) { // 10MB
                    // Keep only last 1000 lines
                    $lines = file($logFile);
                    if ($lines) {
                        $lastLines = array_slice($lines, -1000);
                        file_put_contents($logFile, implode('', $lastLines));
                    }
                }
            }
        })->weekly();

        // Clear old cache entries daily
        $schedule->call(function () {
            \Cache::forget('cpr_auto_sync_status');
            \Cache::forget('cpr_last_auto_sync');
        })->dailyAt('00:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
