<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\Facebook\FacebookAdService;

class FacebookAdController extends Controller
{
    protected $facebookAdService;

    public function __construct(FacebookAdService $facebookAdService)
    {
        $this->facebookAdService = $facebookAdService;
    }

    // public function getListCampaigns()
    // {
    //     $campaigns = $this->facebookAdService->getListCampaigns();
    //     return response()->json($campaigns);
    // }

    // public function getDetailCampaign($campaignId)
    // {
    //     $campaign = $this->facebookAdService->getDetailCampaign($campaignId);
    //     return response()->json($campaign);
    // }

    // public function adReport()
    // {
    //     $accounts = $this->facebookAdService->getAdReport();
    //     return response()->json($accounts);
    // }

    public function getDataCampaigns()
    {

        $idAkun = 'act_'.'****************';
        $accounts = $this->facebookAdService->getDataCampaigns($idAkun);
        return response()->json($accounts);
    }
}
